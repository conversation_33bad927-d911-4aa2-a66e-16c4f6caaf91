import logging
import os
from pythonjsonlogger import jsonlogger
JsonFormatter = jsonlogger.JsonFormatter
from contextvars import ContextVar

# Define a ContextVar to store the API Key ID for the current request context
api_key_id_context = ContextVar("api_key_id", default=None)

class APIKeyIDFilter(logging.Filter):
    """
    A logging filter that retrieves the api_key_id from ContextVar
    and adds it as an attribute to log records.
    """
    def filter(self, record):
        # Get the api_key_id from the context variable for the current execution context
        api_key_id = api_key_id_context.get()
        if api_key_id:
            # Add api_key_id as a custom attribute to the log record.
            # JsonFormatter will pick this up if specified in its format.
            record.api_key_id = api_key_id
        else:
            # Ensure the attribute exists even if no API key is present for the request
            record.api_key_id = None
        return True

def configure_logging(use_json_formatter=True):
    """
    Configures the application's logging to output logs to the console,
    automatically including api_key_id when available in the request context.
    
    Args:
        use_json_formatter (bool): Whether to use JSON formatting for logs.
            Default is True. Set to False for testing to make logs more readable.
    """
    root_logger = logging.getLogger()
    # Set the logging level from environment variable (e.g., LOG_LEVEL=INFO) or default to INFO
    root_logger.setLevel(os.environ.get("LOG_LEVEL", "INFO").upper())

    # Clear existing handlers and filters
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
    root_logger.filters.clear()

    # Configure the Console Handler
    console_handler = logging.StreamHandler()

    if use_json_formatter:
        # Define the format with standard fields and custom fields
        # timestamp, level, logger, message are standard
        # api_key_id is our custom field added by the filter
        format_str = '%(timestamp)s %(level)s %(name)s %(message)s %(api_key_id)s'
        formatter = JsonFormatter(
            format_str,
            rename_fields={
                'asctime': 'timestamp',
                'levelname': 'level',
                'name': 'logger'
            },
            timestamp=True
        )
    else:
        # Use a standard formatter for testing
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s - API Key: %(api_key_id)s'
        )

    # Create and add the API key filter
    api_key_filter = APIKeyIDFilter()
    
    # Add filter to both the handler and root logger
    console_handler.addFilter(api_key_filter)
    root_logger.addFilter(api_key_filter)

    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Create and add the API key filter
    api_key_filter = APIKeyIDFilter()
    
    # Add filter to both the handler and root logger
    console_handler.addFilter(api_key_filter)
    root_logger.addFilter(api_key_filter)
    
    # Add filter to all existing loggers
    for name in logging.root.manager.loggerDict:
        logger = logging.getLogger(name)
        logger.addFilter(api_key_filter)
    
    mode = "JSON" if use_json_formatter else "standard"
    root_logger.info(f"{mode} structured logging configured with API Key ID support.")

def get_logger(name):
    """
    Returns a logger instance for a given module name.
    """
    return logging.getLogger(name)
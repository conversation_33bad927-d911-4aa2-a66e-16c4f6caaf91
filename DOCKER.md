# Docker Deployment Guide

This guide explains how to deploy the Document Analyzer API using Docker.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed on your system
- Google Cloud credentials (if using Google Cloud Storage and Google Generative AI)

## Dockerfile Options

This project includes two Dockerfile options:

1. **Dockerfile** (default): Minimal image without PDF processing capabilities
   - Smaller image size
   - Fewer dependencies
   - Suitable when PDF processing is not needed in production

2. **Dockerfile.pdf**: Complete image with PDF processing capabilities
   - Includes poppler-utils for PDF processing
   - Larger image size
   - Required if you need to use the PDF processing features

## Quick Start

1. Build and start the containers using the default Dockerfile (without PDF processing):

```bash
docker-compose up -d
```

2. The API will be available at http://localhost:8000

3. Create an initial API key:

```bash
docker-compose exec app python scripts/create_initial_api_key.py
```

### Using the PDF-enabled Version

To use the version with PDF processing capabilities:

1. Edit docker-compose.yml to uncomment the app-with-pdf service and comment out the default app service

2. Start the containers:

```bash
docker-compose up -d
```

## Configuration

### Environment Variables

Configure the application by setting environment variables in the `docker-compose.yml` file or by creating a `.env` file in the project root.

Important environment variables:

- `APP_ENVIRONMENT`: Set to `production` for production deployment
- `DB_USERNAME`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`, `DB_DATABASE`: Database connection settings
- `GUNICORN_WORKERS`: Number of Gunicorn worker processes (default: 4)
- `GOOGLE_API_KEY`: Your Google AI API key (for standard Google AI API)
- `GOOGLE_GENAI_USE_VERTEXAI`: Set to "True" to use Vertex AI instead of standard Google AI API
- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID (for Vertex AI)
- `GOOGLE_CLOUD_LOCATION`: Google Cloud region (for Vertex AI)
- `GOOGLE_CLOUD_STORAGE_BUCKET`: Your Google Cloud Storage bucket name

## Building the Docker Image

To build the Docker image without using Docker Compose:

### Default Version (without PDF processing)

```bash
docker build -t document-analyzer:latest .
```

### PDF-Enabled Version

```bash
docker build -t document-analyzer:pdf -f Dockerfile.pdf .
```

## Building Multi-Architecture Images (ARM and x86)

To build Docker images that work on both ARM (Apple Silicon/M1/M2, AWS Graviton) and x86/AMD64 platforms, you can use Docker Buildx:

### Prerequisites

1. Make sure you have Docker Desktop (Mac/Windows) or Docker Engine with Buildx installed:

```bash
docker buildx version
```

2. Create a new builder instance with multi-platform support:

```bash
docker buildx create --name multiplatform-builder --use
```

3. Inspect the builder to verify platform support:

```bash
docker buildx inspect --bootstrap
```

### Building Multi-Platform Images

#### Default Version (without PDF processing)

```bash
docker buildx build --platform linux/amd64,linux/arm64 \
  -t document-analyzer:latest \
  --push .
```

#### PDF-Enabled Version

```bash
docker buildx build --platform linux/amd64,linux/arm64 \
  -t document-analyzer:pdf \
  -f Dockerfile.pdf \
  --push .
```

> **Note**: The `--push` flag requires you to be logged into a Docker registry (Docker Hub, GitHub Container Registry, etc.). If you're building locally, you can use `--load` instead, but it only works for a single platform at a time.

### Using with a Private Registry

If you're using a private registry:

```bash
# Login to your registry
docker login your-registry.com

# Build and push
docker buildx build --platform linux/amd64,linux/arm64 \
  -t your-registry.com/document-analyzer:latest \
  --push .
```

### Automated Multi-Platform Builds with GitHub Actions

This repository includes a GitHub Actions workflow (`.github/workflows/docker-build.yml`) that automatically builds multi-platform Docker images for both the default and PDF-enabled versions.

The workflow:
1. Builds images for both AMD64 (x86) and ARM64 architectures
2. Pushes images to GitHub Container Registry (ghcr.io)
3. Tags images based on git tags, branches, and commits

To use this workflow:
1. Enable GitHub Actions in your repository
2. Push to the main branch or create a tag to trigger the workflow
3. Pull the images from GitHub Container Registry:

```bash
# Pull the default image
docker pull ghcr.io/your-username/document-analyzer:latest

# Pull the PDF-enabled image
docker pull ghcr.io/your-username/document-analyzer-pdf:latest
```

## Running the Container

To run the container without Docker Compose:

### Default Version (without PDF processing)

```bash
docker run -d \
  --name document-analyzer \
  -p 8000:8000 \
  -e APP_ENVIRONMENT=production \
  -e DB_USERNAME=postgres \
  -e DB_PASSWORD=postgres \
  -e DB_HOST=your-db-host \
  -e DB_PORT=5432 \
  -e DB_DATABASE=document_analyzer_prod \
  -e GUNICORN_WORKERS=4 \
  -e GOOGLE_API_KEY=your_google_api_key \
  -e GOOGLE_CLOUD_STORAGE_BUCKET=your_bucket_name \
  document-analyzer:latest
```

### PDF-Enabled Version

```bash
docker run -d \
  --name document-analyzer-pdf \
  -p 8000:8000 \
  -e APP_ENVIRONMENT=production \
  -e DB_USERNAME=postgres \
  -e DB_PASSWORD=postgres \
  -e DB_HOST=your-db-host \
  -e DB_PORT=5432 \
  -e DB_DATABASE=document_analyzer_prod \
  -e GUNICORN_WORKERS=4 \
  -e GOOGLE_API_KEY=your_google_api_key \
  -e GOOGLE_CLOUD_STORAGE_BUCKET=your_bucket_name \
  document-analyzer:pdf
```

## Production Deployment Considerations

### Database

For production, use a managed PostgreSQL service or a properly configured PostgreSQL instance with:
- Regular backups
- High availability setup
- Proper security configurations

### Security

- Never store sensitive credentials in the Docker image
- Use environment variables or secrets management for all credentials
- Configure proper network security rules
- Use HTTPS in production with a valid SSL certificate

#### Handling Google Application Credentials

For Google Cloud services, the application requires a service account key file. Here are the recommended approaches for production:

1. **Using Volume Mounts (Recommended for Docker Compose)**

   Create a `.env` file on your production server with:
   ```
   GOOGLE_CREDENTIALS_PATH=/secure/path/on/production/server/credentials.json
   ```

   The docker-compose.yml is already configured to:
   - Mount this file into the container at `/app/credentials/service-account.json`
   - Set `GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json`

2. **Using Docker Secrets (For Docker Swarm)**

   ```yaml
   services:
     app:
       # ... other configuration
       secrets:
         - google_credentials
       environment:
         - GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/google_credentials

   secrets:
     google_credentials:
       file: ./path/to/credentials.json
   ```

3. **Using Cloud Provider Secret Management**

   For Kubernetes or cloud environments, use:
   - Kubernetes Secrets
   - GCP Secret Manager
   - AWS Secrets Manager
   - HashiCorp Vault

4. **Using Workload Identity (Best for GCP)**

   If deploying to Google Cloud, configure Workload Identity to avoid managing credential files entirely.

### Scaling

The application can be scaled horizontally by running multiple containers behind a load balancer.

#### Adjusting Worker Count

For optimal performance, adjust the `GUNICORN_WORKERS` environment variable based on your server's resources:

- A common formula is `(2 x number_of_cores) + 1`
- For a 2-core server: set `GUNICORN_WORKERS=5`
- For a 4-core server: set `GUNICORN_WORKERS=9`
- For an 8-core server: set `GUNICORN_WORKERS=17`

Too many workers can lead to memory issues, while too few can limit throughput.

### Monitoring

Consider adding monitoring solutions:
- Prometheus for metrics
- Grafana for visualization
- Loki for log aggregation

## Troubleshooting

### Container won't start

Check the logs:

```bash
docker-compose logs app
```

### Database connection issues

Ensure the database is running and accessible:

```bash
docker-compose logs db
```

#### Connecting to PostgreSQL on the Host Machine

When running Docker with Colima and trying to connect to a PostgreSQL database on your host machine:

1. **Find the host IP address from the container's perspective**:

   ```bash
   # Get the Colima VM IP address
   colima list
   ```

2. **Use the host IP address in your configuration**:

   In docker-compose.yml:
   ```yaml
   environment:
     - DB_HOST=************  # Replace with your actual host IP
   ```

3. **Ensure PostgreSQL is configured to accept remote connections**:

   Edit your PostgreSQL configuration file (postgresql.conf):
   ```
   listen_addresses = '*'
   ```

   And update your pg_hba.conf file to allow connections from the Docker subnet:
   ```
   host    all             all             ************/24          md5
   ```

4. **Restart PostgreSQL after making these changes**:

   ```bash
   sudo service postgresql restart
   ```

> **Note**: The IP address `************` is typically the host machine's IP address from the Colima VM's perspective. Your actual IP may differ.

### API key issues

If you can't access protected endpoints, create a new API key:

```bash
docker-compose exec app python scripts/create_initial_api_key.py
```

import pytest
import logging
import uuid
import json
import io
import sys
from unittest.mock import patch, MagicMock
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.utils.logging_config import (
    configure_logging,
    api_key_id_context,
    request_id_context,
    endpoint_context,
    method_context,
    user_agent_context,
    RequestContextFilter
)
from app.middleware.api_key_auth import APIKeyMiddleware
from app.models.database_models import APIKey

@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return MagicMock(spec=Session)

@pytest.fixture
def mock_api_key():
    """Create a mock API key."""
    api_key = MagicMock(spec=APIKey)
    api_key.id = uuid.uuid4()
    api_key.is_active = True
    return api_key

def test_context_filter_functionality(mock_api_key):
    """Test that the RequestContextFilter adds context to log records."""
    # Create a log record
    record = logging.LogRecord(
        name="test_logger",
        level=logging.INFO,
        pathname="test.py",
        lineno=1,
        msg="Test message",
        args=(),
        exc_info=None
    )

    # Set test context values
    test_api_key_id = str(mock_api_key.id)
    test_request_id = str(uuid.uuid4())
    test_endpoint = "/api/test"
    test_method = "POST"
    test_user_agent = "test-client/1.0"

    # Set context variables
    api_key_id_context.set(test_api_key_id)
    request_id_context.set(test_request_id)
    endpoint_context.set(test_endpoint)
    method_context.set(test_method)
    user_agent_context.set(test_user_agent)

    try:
        # Apply the filter
        context_filter = RequestContextFilter()
        context_filter.filter(record)

        # Verify all context fields are present
        assert hasattr(record, 'api_key_id')
        assert hasattr(record, 'request_id')
        assert hasattr(record, 'endpoint')
        assert hasattr(record, 'method')
        assert hasattr(record, 'user_agent')

        # Verify context values
        assert record.api_key_id == test_api_key_id
        assert record.request_id == test_request_id
        assert record.endpoint == test_endpoint
        assert record.method == test_method
        assert record.user_agent == test_user_agent

    finally:
        # Reset context variables
        api_key_id_context.set(None)
        request_id_context.set(None)
        endpoint_context.set(None)
        method_context.set(None)
        user_agent_context.set(None)


def test_context_filter_with_invalid_api_key():
    """Test that the RequestContextFilter handles invalid API key."""
    # Create a log record
    record = logging.LogRecord(
        name="test_logger",
        level=logging.INFO,
        pathname="test.py",
        lineno=1,
        msg="Test message",
        args=(),
        exc_info=None
    )

    # Set invalid API key context
    api_key_id_context.set("invalid")

    try:
        # Apply the filter
        context_filter = RequestContextFilter()
        context_filter.filter(record)

        # Verify invalid API key is logged
        assert hasattr(record, 'api_key_id')
        assert record.api_key_id == "invalid"

    finally:
        # Reset context
        api_key_id_context.set(None)


def test_context_filter_with_null_api_key():
    """Test that the RequestContextFilter handles null API key."""
    # Create a log record
    record = logging.LogRecord(
        name="test_logger",
        level=logging.INFO,
        pathname="test.py",
        lineno=1,
        msg="Test message",
        args=(),
        exc_info=None
    )

    # Set API key context to None (unauthenticated)
    api_key_id_context.set(None)

    try:
        # Apply the filter
        context_filter = RequestContextFilter()
        context_filter.filter(record)

        # Verify API key is None
        assert hasattr(record, 'api_key_id')
        assert record.api_key_id is None

    finally:
        # Reset context
        api_key_id_context.set(None)


def test_logging_configuration():
    """Test that logging configuration works without errors."""
    # Test JSON formatter
    try:
        configure_logging(use_json_formatter=True)
        logger = logging.getLogger("test_json_config")
        logger.info("JSON configuration test")
        assert True  # If we get here, configuration worked
    except Exception as e:
        pytest.fail(f"JSON logging configuration failed: {e}")

    # Test standard formatter
    try:
        configure_logging(use_json_formatter=False)
        logger = logging.getLogger("test_standard_config")
        logger.info("Standard configuration test")
        assert True  # If we get here, configuration worked
    except Exception as e:
        pytest.fail(f"Standard logging configuration failed: {e}")

"""
Google Cloud Storage Service.

This module provides functionality to upload files to Google Cloud Storage.
"""

import os
import uuid
import datetime
from typing import Binary<PERSON>, Optional, Tuple
from fastapi import UploadFile, HTTPException
from google.cloud import storage


def get_storage_client() -> storage.Client:
    """
    Get a configured Google Cloud Storage client.

    Returns:
        storage.Client: Configured Google Cloud Storage client

    Raises:
        HTTPException: If there's an error configuring the client
    """
    try:
        return storage.Client()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error configuring Google Cloud Storage client: {str(e)}"
        )


def get_bucket_name() -> str:
    """
    Get the Google Cloud Storage bucket name from environment variables.

    Returns:
        str: Bucket name

    Raises:
        HTTPException: If the bucket name is not configured
    """
    bucket_name = os.getenv("GOOGLE_CLOUD_STORAGE_BUCKET")
    if not bucket_name:
        raise HTTPException(
            status_code=500,
            detail="Google Cloud Storage bucket name not found. Please set the GOOGLE_CLOUD_STORAGE_BUCKET environment variable."
        )
    return bucket_name


async def upload_file(
    file: UploadFile,
    folder: str = "uploads",
    custom_filename: Optional[str] = None
) -> Tuple[str, str]:
    """
    Upload a file to Google Cloud Storage.

    Args:
        file (UploadFile): The file to upload
        folder (str): The folder within the bucket to store the file (default: "uploads")
        custom_filename (Optional[str]): Custom filename to use (default: None, will use a UUID)

    Returns:
        Tuple[str, str]: A tuple containing (gs_uri, public_url)
            - gs_uri: The Google Storage URI (gs://bucket-name/path/to/file)
            - public_url: The public URL to access the file

    Raises:
        HTTPException: If there's an error uploading the file
    """
    try:
        # Get the storage client and bucket
        storage_client = get_storage_client()
        bucket_name = get_bucket_name()
        bucket = storage_client.bucket(bucket_name)

        # Generate a unique filename if not provided
        if custom_filename:
            # Clean the filename to avoid path traversal
            filename = os.path.basename(custom_filename)
        else:
            # Start with original filename and append a shortened UUID, keeping the original file extension
            original_filename = file.filename or "file"
            original_name = os.path.splitext(original_filename)[0]
            original_extension = os.path.splitext(original_filename)[1] if original_filename else ""
            # Clean the original name to avoid special characters
            clean_name = ''.join(c if c.isalnum() or c in ['-', '_'] else '_' for c in original_name)
            # Use only the first 8 characters of the UUID for a shorter but still unique identifier
            short_uuid = str(uuid.uuid4())[:8]
            filename = f"{clean_name}_{short_uuid}{original_extension}"

        # Create the full path within the bucket
        blob_name = f"{folder}/{filename}" if folder else filename
        blob = bucket.blob(blob_name)

        # Read the file content
        content = await file.read()

        # Upload the file
        blob.upload_from_string(
            content,
            content_type=file.content_type
        )

        # Generate the gs:// URI and public URL
        gs_uri = f"gs://{bucket_name}/{blob_name}"
        public_url = f"https://storage.googleapis.com/{bucket_name}/{blob_name}"

        return gs_uri, public_url

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error uploading file to Google Cloud Storage: {str(e)}"
        )


def generate_signed_url(
    blob_name: str,
    expiration: int = 3600,
    content_type: Optional[str] = None,
    download: bool = False
) -> str:
    """
    Generate a signed URL for a Google Cloud Storage object.

    Args:
        blob_name (str): The name of the blob in the bucket
        expiration (int): URL expiration time in seconds (default: 1 hour)
        content_type (Optional[str]): Content type for the signed URL
        download (bool): Whether to force download instead of viewing in browser

    Returns:
        str: Signed URL for the blob

    Raises:
        HTTPException: If there's an error generating the signed URL
    """
    try:
        # Get the storage client and bucket
        storage_client = get_storage_client()
        bucket_name = get_bucket_name()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_name)

        # Set up query parameters
        query_parameters = {}

        # Add content type if provided
        if content_type:
            query_parameters["response-content-type"] = content_type

        # Add content disposition for downloads
        if download:
            filename = blob_name.split('/')[-1]
            query_parameters["response-content-disposition"] = f"attachment; filename={filename}"

        # Generate the signed URL
        url = blob.generate_signed_url(
            version="v4",
            expiration=datetime.timedelta(seconds=expiration),
            method="GET",
            query_parameters=query_parameters
        )

        return url
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating signed URL: {str(e)}"
        )

"""
Tests for the Validation Repository.

This module contains unit tests for the validation repository.
"""

import pytest
from unittest.mock import MagicMock
from sqlalchemy.orm import Session
from sqlalchemy import and_
import uuid

from app.repositories.validation_repository import (
    create_validation_result,
    get_validation_results_by_log_id,
    get_validation_result,
    update_validation_result
)
from app.models.database_models import ValidationResult


def test_create_validation_result():
    """Test creating a validation result."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a UUID for the document processing log ID
    log_id = uuid.uuid4()

    # Create a validation result
    result = create_validation_result(
        db=db,
        document_processing_log_id=log_id,
        field_key="invoice_number",
        is_valid=True
    )

    # Assert the result was created with the correct values
    assert result.document_processing_log_id == log_id
    assert result.field_key == "invoice_number"
    assert result.is_valid is True

    # Assert the database operations were called
    db.add.assert_called_once_with(result)
    db.commit.assert_called_once()
    db.refresh.assert_called_once_with(result)


def test_get_validation_results_by_log_id():
    """Test getting validation results by log ID."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a UUID for the document processing log ID
    log_id = uuid.uuid4()

    # Mock query result
    mock_results = [
        MagicMock(spec=ValidationResult),
        MagicMock(spec=ValidationResult)
    ]
    db.query.return_value.filter.return_value.all.return_value = mock_results

    # Get validation results
    results = get_validation_results_by_log_id(db, document_processing_log_id=log_id)

    # Assert the results were returned
    assert results == mock_results

    # Assert the query was called correctly
    db.query.assert_called_once_with(ValidationResult)
    db.query.return_value.filter.assert_called_once()


def test_get_validation_result():
    """Test getting a specific validation result."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a UUID for the document processing log ID
    log_id = uuid.uuid4()

    # Mock query result
    mock_result = MagicMock(spec=ValidationResult)
    db.query.return_value.filter.return_value.first.return_value = mock_result

    # Get validation result
    result = get_validation_result(db, document_processing_log_id=log_id, field_key="invoice_number")

    # Assert the result was returned
    assert result == mock_result

    # Assert the query was called correctly
    db.query.assert_called_once_with(ValidationResult)
    db.query.return_value.filter.assert_called_once()


def test_update_validation_result():
    """Test updating a validation result."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock validation result
    mock_validation = MagicMock(spec=ValidationResult)
    db.query.return_value.filter.return_value.first.return_value = mock_validation

    # Update validation result
    result = update_validation_result(db, validation_id=1, is_valid=False)

    # Assert the result was updated
    assert result == mock_validation
    assert result.is_valid is False

    # Assert the database operations were called
    db.commit.assert_called_once()
    db.refresh.assert_called_once_with(mock_validation)

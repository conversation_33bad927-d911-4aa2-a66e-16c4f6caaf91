"""
LLM configuration models for the Document Analyzer API.

This module contains Pydantic models for LLM configuration-related data structures.
"""

from typing import Optional
from pydantic import BaseModel, Field


class LLMConfigurationBase(BaseModel):
    """Base model for LLM configuration."""
    model_id: str
    provider: str
    system_instruction: str
    document_type: str = "standard_invoice"  # Default to standard_invoice for backward compatibility
    temperature: float = 0.7
    top_p: float = 1.0
    top_k: Optional[int] = None


class LLMConfigurationCreate(LLMConfigurationBase):
    """Model for creating a new LLM configuration."""
    pass


class LLMConfigurationUpdate(BaseModel):
    """Model for updating an existing LLM configuration."""
    model_id: Optional[str] = None
    provider: Optional[str] = None
    system_instruction: Optional[str] = None
    document_type: Optional[str] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    top_k: Optional[int] = None


class LLMConfiguration(LLMConfigurationBase):
    """Model for an LLM configuration with database fields."""
    id: int

    model_config = {
        "from_attributes": True
    }

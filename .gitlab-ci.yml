# GitLab CI/CD Pipeline for Document Analyzer

# Define stages
stages:
  - test
  - build
  - deploy  # For future deployment stages

# Variables used across jobs
variables:
  # Docker image name
  IMAGE_NAME: document-analyzer
  # Docker registry path
  REGISTRY_PATH: $CI_REGISTRY_IMAGE
  # Python version
  PYTHON_VERSION: "3.12"
  # Docker image tag
  IMAGE_TAG: $CI_COMMIT_REF_SLUG
  # Cache paths
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.pip-cache"
  # Test reports
  TEST_REPORT: "pytest-report.xml"
  # Environment variables for tests
  APP_ENVIRONMENT: "test"
  DB_USERNAME: "postgres"
  DB_PASSWORD: "postgres"
  DB_HOST: "localhost"
  DB_PORT: "5432"
  DB_DATABASE: "document_analyzer_test"

# Default configuration to be extended
.default-config:
  # Only run on merge requests to main/master and direct pushes to main/master
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /^(main|master)$/
    - if: $CI_COMMIT_BRANCH =~ /^(main|master)$/

# Cache configuration for pip dependencies
.pip-cache:
  extends: .default-config
  cache:
    key: pip-cache-$CI_COMMIT_REF_SLUG
    paths:
      - .pip-cache/
    policy: pull-push

# Cache configuration for Docker layers
.docker-cache:
  extends: .default-config
  cache:
    key: docker-cache-$CI_COMMIT_REF_SLUG
    paths:
      - .docker-cache/
    policy: pull-push

# Test stage
test:
  stage: test
  image: python:$PYTHON_VERSION
  extends: .pip-cache
  services:
    - postgres:16-alpine
  variables:
    # PostgreSQL service configuration
    POSTGRES_USER: $DB_USERNAME
    POSTGRES_PASSWORD: $DB_PASSWORD
    POSTGRES_DB: $DB_DATABASE
  before_script:
    # Install dependencies
    - pip install --cache-dir=$PIP_CACHE_DIR -r requirements.txt
    - pip install pytest pytest-asyncio pytest-cov pytest-xdist
    # Create test fixtures directory if it doesn't exist
    - mkdir -p tests/fixtures
  script:
    # Run tests with coverage and generate JUnit report
    - pytest -v --cov=app --cov-report=term --cov-report=xml:coverage.xml --junitxml=$TEST_REPORT
  artifacts:
    reports:
      junit: $TEST_REPORT
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - coverage.xml
      - $TEST_REPORT
    expire_in: 1 week

# Build stage
build:
  stage: build
  image: docker:latest
  extends: .docker-cache
  services:
    - docker:dind
  variables:
    # Use BuildKit for more efficient builds
    DOCKER_BUILDKIT: 1
    # Use the Docker driver for BuildKit
    DOCKER_DRIVER: overlay2
    # Use the Docker cache directory
    DOCKER_CACHE_DIR: .docker-cache
  before_script:
    # Install dependencies
    - apk add --no-cache curl jq
    # Log in to GitLab Container Registry
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # Set version tag based on git tag or commit
    - |
      if [[ -n "$CI_COMMIT_TAG" ]]; then
        # If this is a tag, use the tag as version
        VERSION=$CI_COMMIT_TAG
      else
        # Otherwise use commit short SHA
        VERSION=$CI_COMMIT_SHORT_SHA
      fi
  script:
    # Build the standard Docker image
    - |
      echo "Building standard Docker image..."
      docker build \
        --cache-from $REGISTRY_PATH/$IMAGE_NAME:latest \
        --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
        --build-arg VCS_REF=$CI_COMMIT_SHORT_SHA \
        --build-arg VERSION=$VERSION \
        --tag $REGISTRY_PATH/$IMAGE_NAME:$VERSION \
        --tag $REGISTRY_PATH/$IMAGE_NAME:$IMAGE_TAG \
        --tag $REGISTRY_PATH/$IMAGE_NAME:latest \
        .

    # Push the standard Docker image to the GitLab Container Registry
    - |
      echo "Pushing standard Docker image..."
      docker push $REGISTRY_PATH/$IMAGE_NAME:$VERSION
      docker push $REGISTRY_PATH/$IMAGE_NAME:$IMAGE_TAG
      docker push $REGISTRY_PATH/$IMAGE_NAME:latest

    # Build the PDF-enabled Docker image
    - |
      echo "Building PDF-enabled Docker image..."
      docker build \
        --file pdf-color-detection.Dockerfile \
        --cache-from $REGISTRY_PATH/$IMAGE_NAME-pdf:latest \
        --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
        --build-arg VCS_REF=$CI_COMMIT_SHORT_SHA \
        --build-arg VERSION=$VERSION \
        --tag $REGISTRY_PATH/$IMAGE_NAME-pdf:$VERSION \
        --tag $REGISTRY_PATH/$IMAGE_NAME-pdf:$IMAGE_TAG \
        --tag $REGISTRY_PATH/$IMAGE_NAME-pdf:latest \
        .

    # Push the PDF-enabled Docker image to the GitLab Container Registry
    - |
      echo "Pushing PDF-enabled Docker image..."
      docker push $REGISTRY_PATH/$IMAGE_NAME-pdf:$VERSION
      docker push $REGISTRY_PATH/$IMAGE_NAME-pdf:$IMAGE_TAG
      docker push $REGISTRY_PATH/$IMAGE_NAME-pdf:latest

    # Output image information
    - |
      echo "Successfully built and pushed the following images:"
      echo "- $REGISTRY_PATH/$IMAGE_NAME:$VERSION"
      echo "- $REGISTRY_PATH/$IMAGE_NAME:$IMAGE_TAG"
      echo "- $REGISTRY_PATH/$IMAGE_NAME:latest"
      echo "- $REGISTRY_PATH/$IMAGE_NAME-pdf:$VERSION"
      echo "- $REGISTRY_PATH/$IMAGE_NAME-pdf:$IMAGE_TAG"
      echo "- $REGISTRY_PATH/$IMAGE_NAME-pdf:latest"

# Deployment placeholder - Uncomment and customize when ready to implement deployment
# deploy:
#   stage: deploy
#   image: alpine:latest
#   variables:
#     # Deployment-specific variables
#     DEPLOY_ENV: staging
#   before_script:
#     - apk add --no-cache curl bash
#   script:
#     - echo "Deploying application to $DEPLOY_ENV environment..."
#     # Add deployment commands here
#   environment:
#     name: staging
#     url: https://staging.example.com
#   rules:
#     - if: $CI_COMMIT_BRANCH == "main"
#       when: manual
#   needs:
#     - build

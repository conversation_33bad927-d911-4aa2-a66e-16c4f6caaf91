"""create document processing logs

Revision ID: 6b220e450144
Revises: f7d2e23aee66
Create Date: 2025-05-07 21:02:34.112136

"""
from typing import Sequence, Union
import uuid

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.sql.sqltypes import TIMESTAMP


# revision identifiers, used by Alembic.
revision: str = '6b220e450144'
down_revision: Union[str, None] = 'f7d2e23aee66'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('document_processing_logs',
        sa.Column('id', UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=False),
        sa.Column('blob_name', sa.String(), nullable=False),
        sa.Column('document_size', sa.Integer(), nullable=False),
        sa.Column('llm_configuration_id', sa.Integer(), nullable=False),
        sa.Column('raw_llm_response', JSONB(), nullable=True),
        sa.Column('extracted_data', JSONB(), nullable=True),
        sa.Column('color_analysis_results', JSONB(), nullable=True),
        sa.Column('processing_time', sa.Float(), nullable=True),
        sa.Column('created_at', TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['llm_configuration_id'], ['llm_configurations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    op.drop_table('document_processing_logs')

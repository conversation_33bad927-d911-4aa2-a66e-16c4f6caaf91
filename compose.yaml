version: '3.8'

services:
  backend:
    image: registry-gitlab.happyfresh.net/hf/tpd/rainmakers/document-analyzer:latest
    container_name: document-analyzer-backend
    ports:
      - "8000:8000"
    environment:
      - APP_ENVIRONMENT=production
      - DB_USERNAME=fajarb
      - DB_PASSWORD=password01
      # For Colima, use the host machine's IP address from the container's perspective
      - DB_HOST=************
      - DB_PORT=5432
      - DB_DATABASE=document_analyzer_dev
      - GUNICORN_WORKERS=4  # Set the number of Gunicorn workers
      - GOOGLE_GENAI_USE_VERTEXAI=True
      - GOOGLE_CLOUD_PROJECT=project-id
      - GOOGLE_CLOUD_LOCATION=us-west1
      # For production, use a mounted volume for credentials instead of a local path
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
      - GOOGLE_CLOUD_STORAGE_BUCKET=bucket-name
    volumes:
      - ./:/app
      # Mount credentials from host to container
      # In production, replace with the actual path to your credentials file
      - ${GOOGLE_CREDENTIALS_PATH:-./credentials/service-account.json}:/app/credentials/service-account.json:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s

  frontend:
    image: registry-gitlab.happyfresh.net/hf/tpd/rainmakers/document-analyzer-admin:latest
    container_name: document-analyzer-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - BACKEND_HOST=http://backend:8000
      - BACKEND_API_KEY=17EEyZKtUSfCspcVISPqAneIO5kdQrXV
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      # Ensures the backend service is started before the frontend service.
      - backend
    # Add any other frontend-specific configurations here (e.g., volumes)

# You can define networks or volumes here if needed
# networks:
#   app-network:
#     driver: bridge

# volumes:
#   some-data:
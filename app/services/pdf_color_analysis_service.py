"""
PDF Color Analysis Service

This module provides functionality to analyze PDF documents and identify pages that contain color.
It uses pdf2image to convert PDF pages to images and then analyzes the pixel data to detect color.
"""

import os
import numpy as np
from typing import Dict, List, Tuple, Union, Optional
from pdf2image import convert_from_path
from PIL import Image


def is_color_pixel(pixel: Tuple[int, int, int], threshold: float = 0.01) -> bool:
    """
    Check if a pixel is colored (not grayscale).

    A pixel is considered grayscale if the difference between its RGB components
    and their average is within the specified threshold.

    Args:
        pixel: RGB tuple (r, g, b)
        threshold: Maximum allowed deviation from average (default: 0.01 or 1%)

    Returns:
        bool: True if the pixel is colored, False if grayscale
    """
    r, g, b = pixel
    avg = (r + g + b) / 3.0

    # If the pixel is very dark or very light, it's likely grayscale
    if avg < 10 or avg > 245:
        return False

    # Check if any color component deviates from the average by more than the threshold
    # Threshold is a percentage (0-1) of the maximum possible difference (255)
    max_diff = max(abs(r - avg), abs(g - avg), abs(b - avg))
    return max_diff > (threshold * 255)


def analyze_image(image: Image.Image, threshold: float = 0.01, sample_rate: float = 0.1) -> Tuple[bool, float, int]:
    """
    Analyze an image to determine if it contains color.

    Args:
        image: PIL Image object
        threshold: Threshold for color detection (default: 0.01 or 1%)
        sample_rate: Fraction of pixels to sample (default: 0.1 or 10%)

    Returns:
        tuple: (is_color, color_percentage, total_pixels_analyzed)
    """
    width, height = image.size
    total_pixels = width * height
    sample_size = int(total_pixels * sample_rate)

    # Convert image to RGB mode if it's not already
    if image.mode != 'RGB':
        image = image.convert('RGB')

    # Sample pixels randomly
    pixels = []
    for _ in range(sample_size):
        x = np.random.randint(0, width)
        y = np.random.randint(0, height)
        pixels.append(image.getpixel((x, y)))

    # Count colored pixels
    color_pixels = sum(1 for pixel in pixels if is_color_pixel(pixel, threshold))
    color_percentage = (color_pixels / len(pixels)) * 100

    # Consider the image colored if more than 0.5% of sampled pixels are colored
    is_color = color_percentage > 0.5

    return is_color, color_percentage, len(pixels)


def check_pdf_for_color(pdf_path: str, threshold: float = 0.01, sample_rate: float = 0.1, dpi: int = 72) -> Tuple[bool, List[Dict]]:
    """
    Check if a PDF contains color.

    Args:
        pdf_path: Path to the PDF file
        threshold: Threshold for color detection (default: 0.01 or 1%)
        sample_rate: Fraction of pixels to sample (default: 0.1 or 10%)

    Returns:
        tuple: (is_color, results_per_page)
            is_color: Boolean indicating if the PDF contains any color pages
            results_per_page: List of dictionaries with analysis results for each page

    Raises:
        FileNotFoundError: If the PDF file does not exist
        Exception: If there's an error converting the PDF to images
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    try:
        # Convert PDF pages to images
        images = convert_from_path(pdf_path, dpi=dpi)
    except Exception as e:
        raise Exception(f"Error converting PDF to images: {e}")

    # Analyze each page
    results = []
    for i, image in enumerate(images):
        is_color, color_percentage, pixels_analyzed = analyze_image(
            image, threshold, sample_rate
        )
        results.append({
            'page': i+1,
            'is_color': is_color,
            'color_percentage': color_percentage,
            'pixels_analyzed': pixels_analyzed
        })

    # PDF is considered colored if any page has color
    pdf_has_color = any(result['is_color'] for result in results)

    return pdf_has_color, results


def get_color_pages(pdf_path: str, threshold: float = 0.01, sample_rate: float = 0.1) -> List[int]:
    """
    Get a list of page numbers that contain color in a PDF.

    Args:
        pdf_path: Path to the PDF file
        threshold: Threshold for color detection (default: 0.01 or 1%)
        sample_rate: Fraction of pixels to sample (default: 0.1 or 10%)

    Returns:
        List[int]: List of page numbers (1-based) that contain color

    Raises:
        FileNotFoundError: If the PDF file does not exist
        Exception: If there's an error analyzing the PDF
    """
    _, results = check_pdf_for_color(pdf_path, threshold, sample_rate)
    return [result['page'] for result in results if result['is_color']]


def analyze_pdf_file(
    pdf_path: str,
    threshold: float = 0.01,
    sample_rate: float = 0.1,
    verbose: bool = False
) -> Dict:
    """
    Analyze a PDF file and return a comprehensive report about its color content.

    Args:
        pdf_path: Path to the PDF file
        threshold: Threshold for color detection (default: 0.01 or 1%)
        sample_rate: Fraction of pixels to sample (default: 0.1 or 10%)
        verbose: Whether to print progress information (default: False)

    Returns:
        Dict: A dictionary containing analysis results:
            - has_color: Boolean indicating if the PDF contains any color
            - total_pages: Total number of pages in the PDF
            - color_pages: List of page numbers that contain color
            - color_page_count: Number of pages that contain color
            - detailed_results: List of dictionaries with detailed analysis for each page

    Raises:
        FileNotFoundError: If the PDF file does not exist
        Exception: If there's an error analyzing the PDF
    """
    if verbose:
        print(f"Analyzing PDF: {pdf_path}")

    has_color, detailed_results = check_pdf_for_color(pdf_path, threshold, sample_rate)

    color_pages = [result['page'] for result in detailed_results if result['is_color']]

    if verbose:
        if has_color:
            print(f"PDF contains color on pages: {color_pages}")
        else:
            print("PDF does not contain any color pages")

    return {
        'has_color': has_color,
        'total_pages': len(detailed_results),
        'color_pages': color_pages,
        'color_page_count': len(color_pages),
        'detailed_results': detailed_results
    }

"""
Router for LLM configuration endpoints.

This module contains API endpoints for managing LLM configurations.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.llm_configuration_models import LLMConfiguration, LLMConfigurationCreate, LLMConfigurationUpdate
from app.repositories import llm_configuration_repository

router = APIRouter(
    prefix="/llm-configurations",
    tags=["llm-configurations"],
    responses={404: {"description": "Not found"}},
)


@router.post("/", response_model=LLMConfiguration)
def create_llm_configuration(
    config: LLMConfigurationCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new LLM configuration.
    """
    return llm_configuration_repository.create_llm_configuration(db, config)


@router.get("/", response_model=List[LLMConfiguration])
def get_llm_configurations(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all LLM configurations.
    """
    return llm_configuration_repository.get_all_llm_configurations(db, skip, limit)


@router.get("/latest", response_model=LLMConfiguration)
def get_latest_config(
    document_type: str = Query(..., description="Document type to get configuration for (e.g., standard_invoice, tax_invoice)"),
    db: Session = Depends(get_db)
):
    """
    Get the latest LLM configuration for a specific document type.

    Args:
        document_type: Document type to get configuration for (e.g., standard_invoice, tax_invoice)
    """
    db_config = llm_configuration_repository.get_latest_llm_configuration_by_document_type(db, document_type)
    if not db_config:
        raise HTTPException(status_code=404, detail=f"No LLM configuration found for document type: {document_type}")
    return db_config


@router.get("/document-types", response_model=List[str])
def get_document_types(
    db: Session = Depends(get_db)
):
    """
    Get all unique document types from the LLM configurations.

    Returns:
        List[str]: List of unique document types
    """
    return llm_configuration_repository.get_all_document_types(db)


@router.get("/{config_id}", response_model=LLMConfiguration)
def get_llm_configuration(
    config_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific LLM configuration by ID.
    """
    db_config = llm_configuration_repository.get_llm_configuration(db, config_id)
    if not db_config:
        raise HTTPException(status_code=404, detail="LLM configuration not found")
    return db_config










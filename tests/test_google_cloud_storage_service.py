"""
Tests for the Google Cloud Storage Service.

This module contains unit tests for the Google Cloud Storage service.
"""

import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import UploadFile, HTTPException

from app.services.google_cloud_storage_service import (
    get_storage_client,
    get_bucket_name,
    upload_file,
    generate_signed_url
)


# Tests for get_storage_client function
@patch('app.services.google_cloud_storage_service.storage.Client')
def test_get_storage_client_success(mock_client):
    """Test getting a storage client successfully."""
    # Mock the storage client
    mock_client.return_value = MagicMock()

    # Call the function
    client = get_storage_client()

    # Assert the client was created
    mock_client.assert_called_once()
    assert client is not None


@patch('app.services.google_cloud_storage_service.storage.Client')
def test_get_storage_client_error(mock_client):
    """Test error handling when getting a storage client."""
    # Mock the storage client to raise an exception
    mock_client.side_effect = Exception("Test error")

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        get_storage_client()

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Error configuring Google Cloud Storage client" in str(excinfo.value.detail)


# Tests for get_bucket_name function
@patch.dict(os.environ, {"GOOGLE_CLOUD_STORAGE_BUCKET": "test-bucket"})
def test_get_bucket_name_success():
    """Test getting the bucket name successfully."""
    # Call the function
    bucket_name = get_bucket_name()

    # Assert the bucket name
    assert bucket_name == "test-bucket"


@patch.dict(os.environ, {}, clear=True)
def test_get_bucket_name_missing():
    """Test error handling when the bucket name is missing."""
    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        get_bucket_name()

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Google Cloud Storage bucket name not found" in str(excinfo.value.detail)


# Tests for upload_file function
@patch('app.services.google_cloud_storage_service.get_storage_client')
@patch('app.services.google_cloud_storage_service.get_bucket_name')
@pytest.mark.asyncio
async def test_upload_file_success(mock_get_bucket_name, mock_get_storage_client):
    """Test uploading a file successfully."""
    # Mock the bucket name
    mock_get_bucket_name.return_value = "test-bucket"

    # Mock the storage client and bucket
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_bucket.blob.return_value = mock_blob
    mock_client = MagicMock()
    mock_client.bucket.return_value = mock_bucket
    mock_get_storage_client.return_value = mock_client

    # Mock the file
    mock_file = AsyncMock(spec=UploadFile)
    mock_file.filename = "test.pdf"
    mock_file.content_type = "application/pdf"
    mock_file.read.return_value = b"test content"

    # Call the function
    gs_uri, public_url = await upload_file(mock_file, folder="test-folder")

    # Assert the function calls
    mock_get_storage_client.assert_called_once()
    mock_get_bucket_name.assert_called_once()
    mock_client.bucket.assert_called_once_with("test-bucket")
    mock_bucket.blob.assert_called_once()
    mock_file.read.assert_called_once()
    mock_blob.upload_from_string.assert_called_once_with(
        b"test content",
        content_type="application/pdf"
    )

    # Assert the return values
    assert gs_uri.startswith("gs://test-bucket/test-folder/")
    assert public_url.startswith("https://storage.googleapis.com/test-bucket/test-folder/")


@patch('app.services.google_cloud_storage_service.get_storage_client')
@pytest.mark.asyncio
async def test_upload_file_error(mock_get_storage_client):
    """Test error handling when uploading a file."""
    # Mock the storage client to raise an exception
    mock_get_storage_client.side_effect = Exception("Test error")

    # Mock the file
    mock_file = AsyncMock(spec=UploadFile)
    mock_file.filename = "test.pdf"

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await upload_file(mock_file)

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Error uploading file to Google Cloud Storage" in str(excinfo.value.detail)


@patch('app.services.google_cloud_storage_service.get_storage_client')
@patch('app.services.google_cloud_storage_service.get_bucket_name')
def test_generate_signed_url(mock_get_bucket_name, mock_get_storage_client):
    """Test generating a signed URL."""
    # Mock the bucket name
    mock_get_bucket_name.return_value = "test-bucket"

    # Mock the storage client and bucket
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_blob.generate_signed_url.return_value = "https://storage.googleapis.com/test-bucket/test.pdf?signature=abc123"
    mock_bucket.blob.return_value = mock_blob
    mock_client = MagicMock()
    mock_client.bucket.return_value = mock_bucket
    mock_get_storage_client.return_value = mock_client

    # Call the function
    from app.services.google_cloud_storage_service import generate_signed_url
    url = generate_signed_url("test.pdf", 3600)

    # Assert the function calls
    mock_get_storage_client.assert_called_once()
    mock_get_bucket_name.assert_called_once()
    mock_client.bucket.assert_called_once_with("test-bucket")
    mock_bucket.blob.assert_called_once_with("test.pdf")
    mock_blob.generate_signed_url.assert_called_once()

    # Assert the return value
    assert url == "https://storage.googleapis.com/test-bucket/test.pdf?signature=abc123"


@patch('app.services.google_cloud_storage_service.get_storage_client')
@patch('app.services.google_cloud_storage_service.get_bucket_name')
def test_generate_signed_url_with_content_type(mock_get_bucket_name, mock_get_storage_client):
    """Test generating a signed URL with content type."""
    # Mock the bucket name
    mock_get_bucket_name.return_value = "test-bucket"

    # Mock the storage client and bucket
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_blob.generate_signed_url.return_value = "https://storage.googleapis.com/test-bucket/test.pdf?signature=abc123"
    mock_bucket.blob.return_value = mock_blob
    mock_client = MagicMock()
    mock_client.bucket.return_value = mock_bucket
    mock_get_storage_client.return_value = mock_client

    # Call the function
    from app.services.google_cloud_storage_service import generate_signed_url
    url = generate_signed_url("test.pdf", 3600, "application/pdf")

    # Assert the function calls
    mock_get_storage_client.assert_called_once()
    mock_get_bucket_name.assert_called_once()
    mock_client.bucket.assert_called_once_with("test-bucket")
    mock_bucket.blob.assert_called_once_with("test.pdf")

    # Check that generate_signed_url was called with the correct parameters
    mock_blob.generate_signed_url.assert_called_once()
    call_args = mock_blob.generate_signed_url.call_args[1]
    assert call_args["version"] == "v4"
    assert call_args["method"] == "GET"
    assert "query_parameters" in call_args
    assert "response-content-type" in call_args["query_parameters"]
    assert call_args["query_parameters"]["response-content-type"] == "application/pdf"

    # Assert the return value
    assert url == "https://storage.googleapis.com/test-bucket/test.pdf?signature=abc123"


@patch('app.services.google_cloud_storage_service.get_storage_client')
@patch('app.services.google_cloud_storage_service.get_bucket_name')
def test_generate_signed_url_with_download(mock_get_bucket_name, mock_get_storage_client):
    """Test generating a signed URL with download option."""
    # Mock the bucket name
    mock_get_bucket_name.return_value = "test-bucket"

    # Mock the storage client and bucket
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_blob.generate_signed_url.return_value = "https://storage.googleapis.com/test-bucket/test.pdf?signature=abc123"
    mock_bucket.blob.return_value = mock_blob
    mock_client = MagicMock()
    mock_client.bucket.return_value = mock_bucket
    mock_get_storage_client.return_value = mock_client

    # Call the function
    from app.services.google_cloud_storage_service import generate_signed_url
    url = generate_signed_url("test.pdf", 3600, None, True)

    # Assert the function calls
    mock_get_storage_client.assert_called_once()
    mock_get_bucket_name.assert_called_once()
    mock_client.bucket.assert_called_once_with("test-bucket")
    mock_bucket.blob.assert_called_once_with("test.pdf")

    # Check that generate_signed_url was called with the correct parameters
    mock_blob.generate_signed_url.assert_called_once()
    call_args = mock_blob.generate_signed_url.call_args[1]
    assert call_args["version"] == "v4"
    assert call_args["method"] == "GET"
    assert "query_parameters" in call_args
    assert "response-content-disposition" in call_args["query_parameters"]
    assert "attachment; filename=test.pdf" in call_args["query_parameters"]["response-content-disposition"]

    # Assert the return value
    assert url == "https://storage.googleapis.com/test-bucket/test.pdf?signature=abc123"


@patch('app.services.google_cloud_storage_service.get_storage_client')
def test_generate_signed_url_error(mock_get_storage_client):
    """Test error handling when generating a signed URL."""
    # Mock the storage client to raise an exception
    mock_get_storage_client.side_effect = Exception("Test error")

    # Call the function and expect an HTTPException
    from app.services.google_cloud_storage_service import generate_signed_url
    with pytest.raises(HTTPException) as excinfo:
        generate_signed_url("test.pdf")

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Error generating signed URL" in str(excinfo.value.detail)

"""
Tests for the Invoice Schema classes.

This module contains unit tests for the invoice schema classes.
"""

import pytest
from google.genai import types

from app.services.google_genai.schemas import InvoiceSchema


def test_standard_invoice_schema():
    """Test the standard invoice schema."""
    schema = InvoiceSchema.standard_invoice_schema()

    # Check that it's a Schema object
    assert isinstance(schema, types.Schema)

    # Check that it has the correct type
    assert schema.type == types.Type.OBJECT

    # Check that it has the required properties
    properties = schema.properties
    assert "document_type" in properties
    assert "page_count" in properties
    assert "results" in properties

    # Check that document_type is set correctly
    assert properties["document_type"].description == "The document type (standard_invoice)"

    # Check that results has the required properties
    results_properties = properties["results"].properties
    assert "fields" in results_properties

    # Check that fields has the required properties
    fields_properties = results_properties["fields"].properties
    assert "vendor_name" in fields_properties
    assert "invoice_number" in fields_properties
    assert "invoice_date" in fields_properties
    assert "invoice_amount" in fields_properties
    assert "vat_amount" in fields_properties

    # Check that it doesn't have tax invoice properties
    assert "tax_invoice_number" not in fields_properties
    assert "tax_invoice_date" not in fields_properties

    # Check that fields have validation properties
    field_properties = fields_properties["invoice_number"].properties
    assert "ocr_value" in field_properties


def test_tax_invoice_schema():
    """Test the tax invoice schema."""
    schema = InvoiceSchema.tax_invoice_schema()

    # Check that it's a Schema object
    assert isinstance(schema, types.Schema)

    # Check that it has the correct type
    assert schema.type == types.Type.OBJECT

    # Check that it has the required properties
    properties = schema.properties
    assert "document_type" in properties
    assert "page_count" in properties
    assert "results" in properties

    # Check that document_type is set correctly
    assert properties["document_type"].description == "The document type (tax_invoice)"

    # Check that results has the required properties
    results_properties = properties["results"].properties
    assert "fields" in results_properties

    # Check that fields has the required properties
    fields_properties = results_properties["fields"].properties
    assert "vendor_name" in fields_properties
    assert "tax_invoice_number" in fields_properties
    assert "tax_invoice_date" in fields_properties
    assert "invoice_amount" in fields_properties
    assert "vat_amount" in fields_properties

    # Check that it doesn't have standard invoice properties
    assert "invoice_number" not in fields_properties
    assert "invoice_date" not in fields_properties

    # Check that fields have validation properties
    field_properties = fields_properties["tax_invoice_number"].properties
    assert "ocr_value" in field_properties


def test_standard_invoice_with_confidence_schema():
    """Test the standard invoice with validation schema."""
    schema = InvoiceSchema.standard_invoice_with_confidence_schema()

    # Check that it's a Schema object
    assert isinstance(schema, types.Schema)

    # Check that it has the correct type
    assert schema.type == types.Type.OBJECT

    # Check that it has the required properties
    properties = schema.properties
    assert "document_type" in properties
    assert "page_count" in properties
    assert "results" in properties

    # Check that results has the required properties
    results_properties = properties["results"].properties
    assert "fields" in results_properties

    # Check that fields has the required properties
    fields_properties = results_properties["fields"].properties
    assert "vendor_name" in fields_properties
    assert "invoice_number" in fields_properties
    assert "invoice_date" in fields_properties

    # Check that fields have validation properties
    field_properties = fields_properties["invoice_number"].properties
    assert "ocr_value" in field_properties


def test_tax_invoice_with_confidence_schema():
    """Test the tax invoice with validation schema."""
    schema = InvoiceSchema.tax_invoice_with_confidence_schema()

    # Check that it's a Schema object
    assert isinstance(schema, types.Schema)

    # Check that it has the correct type
    assert schema.type == types.Type.OBJECT

    # Check that it has the required properties
    properties = schema.properties
    assert "document_type" in properties
    assert "page_count" in properties
    assert "results" in properties

    # Check that results has the required properties
    results_properties = properties["results"].properties
    assert "fields" in results_properties

    # Check that fields has the required properties
    fields_properties = results_properties["fields"].properties
    assert "vendor_name" in fields_properties
    assert "tax_invoice_number" in fields_properties
    assert "tax_invoice_date" in fields_properties

    # Check that fields have validation properties
    field_properties = fields_properties["tax_invoice_number"].properties
    assert "ocr_value" in field_properties


def test_legacy_invoice_schema():
    """Test the legacy invoice schema."""
    schema = InvoiceSchema.invoice_schema()

    # Check that it's a Schema object
    assert isinstance(schema, types.Schema)

    # Check that it has the correct type
    assert schema.type == types.Type.OBJECT

    # Check that it has the required properties
    properties = schema.properties
    assert "document_type" in properties
    assert "page_count" in properties
    assert "results" in properties

    # Check that results has the required properties
    results_properties = properties["results"].properties
    assert "fields" in results_properties

    # Check that fields has all properties from both invoice types
    fields_properties = results_properties["fields"].properties
    assert "vendor_name" in fields_properties
    assert "invoice_number" in fields_properties
    assert "invoice_date" in fields_properties
    assert "tax_invoice_number" in fields_properties
    assert "tax_invoice_date" in fields_properties
    assert "invoice_amount" in fields_properties
    assert "vat_amount" in fields_properties

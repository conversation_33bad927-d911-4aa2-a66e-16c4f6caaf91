"""
Database configuration for the Document Analyzer API.

This module contains the SQLAlchemy database configuration and session management.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base

from app.config import settings

# PostgreSQL connection URL from settings
# Format: postgresql://username:password@host:port/database_name
SQLALCHEMY_DATABASE_URL = settings.db.connection_string

# Create SQLAlchemy engine
engine = create_engine(SQLALCHEMY_DATABASE_URL)

# Create a SessionLocal class
# Each instance of this class will be a database session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create a Base class
# This class will be used to create models
Base = declarative_base()

# Dependency to get DB session
def get_db():
    """
    Dependency function to get a database session.

    Yields:
        Session: A SQLAlchemy session
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

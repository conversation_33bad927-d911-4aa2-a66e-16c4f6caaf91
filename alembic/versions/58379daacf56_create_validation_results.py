"""create validation results

Revision ID: 58379daacf56
Revises: 6b220e450144
Create Date: 2025-05-07 21:14:34.496969

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql.sqltypes import TIMESTAMP


# revision identifiers, used by Alembic.
revision: str = '58379daacf56'
down_revision: Union[str, None] = '6b220e450144'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table('validation_results',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('document_processing_log_id', UUID(as_uuid=True), nullable=False),
        sa.Column('field_key', sa.String(), nullable=False),
        sa.Column('is_valid', sa.<PERSON>(), nullable=False),
        sa.Column('validated_at', TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['document_processing_log_id'], ['document_processing_logs.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('document_processing_log_id', 'field_key', name='uix_validation_log_field')
    )
    op.create_index(op.f('ix_validation_results_document_processing_log_id'), 'validation_results', ['document_processing_log_id'], unique=False)


def downgrade() -> None:
    op.drop_index(op.f('ix_validation_results_document_processing_log_id'), table_name='validation_results')
    op.drop_table('validation_results')

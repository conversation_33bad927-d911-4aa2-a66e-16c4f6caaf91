"""
Repository for LLM configuration operations.

This module contains functions for database operations related to LLM configurations.
"""

from typing import List, Optional
from sqlalchemy.orm import Session

from app.models.database_models import LLMConfiguration
from app.models.llm_configuration_models import LLMConfigurationCreate, LLMConfigurationUpdate


def create_llm_configuration(db: Session, config: LLMConfigurationCreate) -> LLMConfiguration:
    """
    Create a new LLM configuration.

    Args:
        db (Session): Database session
        config (LLMConfigurationCreate): LLM configuration data

    Returns:
        LLMConfiguration: Created LLM configuration
    """
    db_config = LLMConfiguration(
        model_id=config.model_id,
        provider=config.provider,
        system_instruction=config.system_instruction,
        document_type=config.document_type,
        temperature=config.temperature,
        top_p=config.top_p,
        top_k=config.top_k
    )
    db.add(db_config)
    db.commit()
    db.refresh(db_config)
    return db_config


def get_llm_configuration(db: Session, config_id: int) -> Optional[LLMConfiguration]:
    """
    Get an LLM configuration by ID.

    Args:
        db (Session): Database session
        config_id (int): LLM configuration ID

    Returns:
        Optional[LLMConfiguration]: LLM configuration if found, None otherwise
    """
    return db.query(LLMConfiguration).filter(LLMConfiguration.id == config_id).first()


def get_llm_configuration_by_model_id(db: Session, model_id: str) -> Optional[LLMConfiguration]:
    """
    Get an LLM configuration by model ID.

    Args:
        db (Session): Database session
        model_id (str): LLM model ID

    Returns:
        Optional[LLMConfiguration]: LLM configuration if found, None otherwise
    """
    return db.query(LLMConfiguration).filter(LLMConfiguration.model_id == model_id).first()


def get_all_llm_configurations(db: Session, skip: int = 0, limit: int = 100) -> List[LLMConfiguration]:
    """
    Get all LLM configurations.

    Args:
        db (Session): Database session
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.

    Returns:
        List[LLMConfiguration]: List of LLM configurations
    """
    return db.query(LLMConfiguration).offset(skip).limit(limit).all()


def update_llm_configuration(
    db: Session, config_id: int, config_update: LLMConfigurationUpdate
) -> Optional[LLMConfiguration]:
    """
    Update an LLM configuration.

    Args:
        db (Session): Database session
        config_id (int): LLM configuration ID
        config_update (LLMConfigurationUpdate): Updated LLM configuration data

    Returns:
        Optional[LLMConfiguration]: Updated LLM configuration if found, None otherwise
    """
    db_config = db.query(LLMConfiguration).filter(LLMConfiguration.id == config_id).first()
    if not db_config:
        return None

    update_data = config_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_config, key, value)

    db.commit()
    db.refresh(db_config)
    return db_config


def delete_llm_configuration(db: Session, config_id: int) -> bool:
    """
    Delete an LLM configuration.

    Args:
        db (Session): Database session
        config_id (int): LLM configuration ID

    Returns:
        bool: True if deleted, False if not found
    """
    db_config = db.query(LLMConfiguration).filter(LLMConfiguration.id == config_id).first()
    if not db_config:
        return False

    db.delete(db_config)
    db.commit()
    return True


def get_latest_llm_configuration(db: Session) -> Optional[LLMConfiguration]:
    """
    Get the latest LLM configuration by ID (highest ID).

    Args:
        db (Session): Database session

    Returns:
        Optional[LLMConfiguration]: Latest LLM configuration if any exists, None otherwise
    """
    return db.query(LLMConfiguration).order_by(LLMConfiguration.id.desc()).first()


def get_latest_llm_configuration_by_document_type(db: Session, document_type: str) -> Optional[LLMConfiguration]:
    """
    Get the latest LLM configuration for a specific document type.

    Args:
        db (Session): Database session
        document_type (str): Document type to filter by (e.g., "standard_invoice", "tax_invoice")

    Returns:
        Optional[LLMConfiguration]: Latest LLM configuration for the specified document type if any exists, None otherwise
    """
    return db.query(LLMConfiguration).filter(
        LLMConfiguration.document_type == document_type
    ).order_by(LLMConfiguration.id.desc()).first()


def get_all_document_types(db: Session) -> List[str]:
    """
    Get all unique document types from the LLM configurations.

    Args:
        db (Session): Database session

    Returns:
        List[str]: List of unique document types
    """
    # Query distinct document types
    result = db.query(LLMConfiguration.document_type).distinct().all()

    # Extract document types from result tuples
    return [r[0] for r in result]

"""
Tests for the Validation Endpoints.

This module contains unit tests for the validation API endpoints.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import uuid

from main import app
from app.database import get_db
from app.models.database_models import DocumentProcessingLog, ValidationResult
from tests.utils import configure_test_client


@pytest.fixture
def test_client():
    """Create a test client with a consistent mock DB session."""
    # Create a fresh mock DB session for each test
    mock_db = MagicMock(spec=Session)

    # Override the database dependency just for this test
    app.dependency_overrides[get_db] = lambda: mock_db

    # Configure the test client with API key authentication
    client, _, patchers = configure_test_client(app, mock_db)

    # Return both the client and the mock DB
    yield client, mock_db

    # Clean up after the test
    app.dependency_overrides.pop(get_db, None)

    # Stop the patchers
    for patcher in patchers:
        patcher.stop()


def test_validate_extracted_field(test_client):
    """Test validating an extracted field."""
    client, mock_db = test_client

    # Create a UUID for the document processing log ID
    log_id = uuid.uuid4()

    # Mock document processing log
    mock_log = MagicMock(spec=DocumentProcessingLog)
    mock_log.id = log_id
    mock_log.extracted_data = {"invoice_number": "INV-123"}

    # Mock validation result
    mock_validation = MagicMock(spec=ValidationResult)
    mock_validation.id = 1
    mock_validation.document_processing_log_id = log_id
    mock_validation.field_key = "invoice_number"
    mock_validation.is_valid = True

    # Mock the repository functions
    with patch('app.routers.documents.get_document_processing_log') as mock_get_log, \
         patch('app.routers.documents.get_validation_result') as mock_get_validation, \
         patch('app.routers.documents.create_validation_result') as mock_create_validation:

        mock_get_log.return_value = mock_log
        mock_get_validation.return_value = None
        mock_create_validation.return_value = mock_validation

        # Make the request with API key header
        response = client.post(
            f"/documents/processing-logs/{log_id}/validations",
            json={"field_key": "invoice_number", "is_valid": True},
            headers={"X-API-Key": "test-api-key"}
        )

        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["document_processing_log_id"] == str(log_id)
        assert data["field_key"] == "invoice_number"
        assert data["is_valid"] is True


def test_update_existing_validation(test_client):
    """Test updating an existing validation."""
    client, mock_db = test_client

    # Create a UUID for the document processing log ID
    log_id = uuid.uuid4()

    # Mock document processing log
    mock_log = MagicMock(spec=DocumentProcessingLog)
    mock_log.id = log_id
    mock_log.extracted_data = {"invoice_number": "INV-123"}

    # Mock existing validation result
    mock_existing = MagicMock(spec=ValidationResult)
    mock_existing.id = 1

    # Mock updated validation result
    mock_updated = MagicMock(spec=ValidationResult)
    mock_updated.id = 1
    mock_updated.document_processing_log_id = log_id
    mock_updated.field_key = "invoice_number"
    mock_updated.is_valid = False

    # Mock the repository functions
    with patch('app.routers.documents.get_document_processing_log') as mock_get_log, \
         patch('app.routers.documents.get_validation_result') as mock_get_validation, \
         patch('app.routers.documents.update_validation_result') as mock_update_validation:

        mock_get_log.return_value = mock_log
        mock_get_validation.return_value = mock_existing
        mock_update_validation.return_value = mock_updated

        # Make the request with API key header
        response = client.post(
            f"/documents/processing-logs/{log_id}/validations",
            json={"field_key": "invoice_number", "is_valid": False},
            headers={"X-API-Key": "test-api-key"}
        )

        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == 1
        assert data["document_processing_log_id"] == str(log_id)
        assert data["field_key"] == "invoice_number"
        assert data["is_valid"] is False

        # Verify update was called with the db from the dependency override
        mock_update_validation.assert_called_once_with(
            db=mock_db,  # Use the mock_db from the fixture
            validation_id=mock_existing.id,
            is_valid=False
        )


def test_validate_nonexistent_log(test_client):
    """Test validating a field for a nonexistent log."""
    client, _ = test_client

    # Create a valid UUID for testing
    test_uuid = uuid.uuid4()

    # Mock the repository function
    with patch('app.routers.documents.get_document_processing_log') as mock_get_log:
        mock_get_log.return_value = None

        # Make the request with API key header
        response = client.post(
            f"/documents/processing-logs/{test_uuid}/validations",
            json={"field_key": "invoice_number", "is_valid": True},
            headers={"X-API-Key": "test-api-key"}
        )

        # Check the response
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]


def test_validate_nonexistent_field(test_client):
    """Test validating a field that doesn't exist in extracted data."""
    client, _ = test_client

    # Create a UUID for the document processing log ID
    log_id = uuid.uuid4()

    # Mock document processing log
    mock_log = MagicMock(spec=DocumentProcessingLog)
    mock_log.id = log_id
    mock_log.extracted_data = {"invoice_number": "INV-123"}

    # Mock the repository function
    with patch('app.routers.documents.get_document_processing_log') as mock_get_log:
        mock_get_log.return_value = mock_log

        # Make the request with API key header
        response = client.post(
            f"/documents/processing-logs/{log_id}/validations",
            json={"field_key": "nonexistent_field", "is_valid": True},
            headers={"X-API-Key": "test-api-key"}
        )

        # Check the response
        assert response.status_code == 400
        assert "not found in extracted data" in response.json()["detail"]

"""
Check environment variables script.

This script checks if environment variables are being loaded correctly.
"""

import os
import sys
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.utils.env_loader import load_env_file
from app.config import settings, get_betterstack_settings, reload_settings

# Load environment variables from .env file
print("Loading environment variables from .env file...")
load_env_file()

# Print environment variables
print("\nEnvironment variables:")
print(f"BETTERSTACK_SOURCE_TOKEN: {'*****' if os.getenv('BETTERSTACK_SOURCE_TOKEN') else 'not set'}")
print(f"BETTERSTACK_HOST: {os.getenv('BETTERSTACK_HOST', 'not set')}")

# Print settings before reload
print("\nSettings from app.config (before reload):")
print(f"settings.betterstack.source_token: {'*****' if settings.betterstack.source_token else 'not set'}")
print(f"settings.betterstack.host: {settings.betterstack.host}")

# Reload settings
print("\nReloading settings...")
reload_settings()

# Print settings after reload
print("\nSettings from app.config (after reload):")
print(f"settings.betterstack.source_token: {'*****' if settings.betterstack.source_token else 'not set'}")
print(f"settings.betterstack.host: {settings.betterstack.host}")

# Get fresh BetterStack settings
print("\nFresh BetterStack settings:")
fresh_settings = get_betterstack_settings()
print(f"fresh_settings.source_token: {'*****' if fresh_settings.source_token else 'not set'}")
print(f"fresh_settings.host: {fresh_settings.host}")

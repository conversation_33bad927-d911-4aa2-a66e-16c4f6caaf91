"""
Tests for the Document Processing Log Repository.

This module contains unit tests for the document processing log repository.
"""

import pytest
from unittest.mock import MagicMock
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from app.repositories.document_processing_log_repository import (
    create_document_processing_log,
    get_document_processing_log,
    get_document_processing_logs_by_blob_name,
    get_paginated_document_processing_logs,
    delete_document_processing_log
)
from app.models.database_models import DocumentProcessing<PERSON>og


def test_create_document_processing_log():
    """Test creating a document processing log."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a document processing log
    log = create_document_processing_log(
        db=db,
        blob_name="documents/test.pdf",
        document_size=1024,
        llm_configuration_id=1,
        raw_llm_response={"vendor_name": "Test Vendor"},
        extracted_data={"vendor_name": "Test Vendor"},
        color_analysis_results={"has_color": True, "total_pages": 3},
        processing_time=1.5
    )

    # Assert the log was created with the correct values
    assert log.blob_name == "documents/test.pdf"
    assert log.document_size == 1024
    assert log.llm_configuration_id == 1
    assert log.raw_llm_response == {"vendor_name": "Test Vendor"}
    assert log.extracted_data == {"vendor_name": "Test Vendor"}
    assert log.color_analysis_results == {"has_color": True, "total_pages": 3}
    assert log.processing_time == 1.5

    # Assert the database operations were called
    db.add.assert_called_once_with(log)
    db.commit.assert_called_once()
    db.refresh.assert_called_once_with(log)


def test_get_document_processing_log():
    """Test getting a document processing log by ID."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a mock log
    mock_log = MagicMock(spec=DocumentProcessingLog)
    mock_log.id = 1
    mock_log.blob_name = "documents/test.pdf"

    # Mock the query result with options for joinedload
    db.query.return_value.options.return_value.filter.return_value.first.return_value = mock_log

    # Get the log
    log = get_document_processing_log(db, 1)

    # Assert the log is the mock log
    assert log == mock_log

    # Assert the query was called with the correct filter
    db.query.assert_called_once_with(DocumentProcessingLog)
    db.query.return_value.options.assert_called_once()
    db.query.return_value.options.return_value.filter.assert_called_once()


def test_get_document_processing_log_not_found():
    """Test getting a document processing log that doesn't exist."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock the query result to return None with options for joinedload
    db.query.return_value.options.return_value.filter.return_value.first.return_value = None

    # Get the log
    log = get_document_processing_log(db, 999)

    # Assert the log is None
    assert log is None

    # Assert the query was called with the correct filter
    db.query.assert_called_once_with(DocumentProcessingLog)
    db.query.return_value.options.assert_called_once()
    db.query.return_value.options.return_value.filter.assert_called_once()


def test_get_document_processing_logs_by_blob_name():
    """Test getting document processing logs by blob name."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create mock logs
    mock_log1 = MagicMock(spec=DocumentProcessingLog)
    mock_log1.id = 1
    mock_log1.blob_name = "documents/test.pdf"

    mock_log2 = MagicMock(spec=DocumentProcessingLog)
    mock_log2.id = 2
    mock_log2.blob_name = "documents/test.pdf"

    # Mock the query result with options for joinedload
    db.query.return_value.options.return_value.filter.return_value.all.return_value = [mock_log1, mock_log2]

    # Get the logs
    logs = get_document_processing_logs_by_blob_name(db, "documents/test.pdf")

    # Assert the logs are the mock logs
    assert len(logs) == 2
    assert logs[0] == mock_log1
    assert logs[1] == mock_log2

    # Assert the query was called with the correct filter
    db.query.assert_called_once_with(DocumentProcessingLog)
    db.query.return_value.options.assert_called_once()
    db.query.return_value.options.return_value.filter.assert_called_once()


def test_get_paginated_document_processing_logs():
    """Test getting paginated document processing logs."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create mock logs
    mock_logs = [MagicMock(spec=DocumentProcessingLog) for _ in range(3)]
    for i, log in enumerate(mock_logs):
        log.id = i + 1
        log.blob_name = f"documents/test{i+1}.pdf"

    # Mock the query results
    db.query.return_value.scalar.return_value = 10  # Total count
    # Add options for joinedload
    db.query.return_value.options.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_logs

    # Get the paginated logs
    logs, total = get_paginated_document_processing_logs(db, skip=0, limit=3)

    # Assert the logs and total count
    assert total == 10
    assert len(logs) == 3
    assert logs == mock_logs

    # Assert the query was called correctly
    assert db.query.call_count == 2  # One for count, one for items

    # Check that options was called for joinedload
    db.query.return_value.options.assert_called_once()

    # Check that order_by was called with desc(created_at)
    db.query.return_value.options.return_value.order_by.assert_called_once()

    # Check that offset and limit were called with the correct values
    db.query.return_value.options.return_value.order_by.return_value.offset.assert_called_once_with(0)
    db.query.return_value.options.return_value.order_by.return_value.offset.return_value.limit.assert_called_once_with(3)


def test_delete_document_processing_log():
    """Test deleting a document processing log."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create mock validation results
    mock_validation1 = MagicMock()
    mock_validation2 = MagicMock()

    # Create a mock log with validation results
    mock_log = MagicMock(spec=DocumentProcessingLog)
    mock_log.id = 1
    mock_log.blob_name = "documents/test.pdf"
    mock_log.validation_results = [mock_validation1, mock_validation2]

    # Mock the query result with options for joinedload
    db.query.return_value.options.return_value.filter.return_value.first.return_value = mock_log

    # Delete the log
    result = delete_document_processing_log(db, 1)

    # Assert the result is True (successful deletion)
    assert result is True

    # Assert the database operations were called
    db.query.assert_called_once_with(DocumentProcessingLog)
    db.query.return_value.options.assert_called_once()

    # Assert that delete was called for each validation result and the log
    assert db.delete.call_count == 3  # 2 validation results + 1 log
    db.commit.assert_called_once()


def test_delete_document_processing_log_not_found():
    """Test deleting a document processing log that doesn't exist."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock the query result to return None with options for joinedload
    db.query.return_value.options.return_value.filter.return_value.first.return_value = None

    # Try to delete the log
    result = delete_document_processing_log(db, 999)

    # Assert the result is False (log not found)
    assert result is False

    # Assert the query was called with the correct filter
    db.query.assert_called_once_with(DocumentProcessingLog)
    db.query.return_value.options.assert_called_once()

    # Assert that delete and commit were not called
    db.delete.assert_not_called()
    db.commit.assert_not_called()

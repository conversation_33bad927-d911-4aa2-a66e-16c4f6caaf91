#!/bin/bash

export AWS_PROFILE="fajar-prod"

# Configuration
AWS_REGION="ap-southeast-1"
AWS_ACCOUNT_ID="************"
ECR_REPOSITORY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"
IMAGE_NAME="ext/ki/document-analyzer"
TAG="latest"
PLATFORMS="linux/amd64,linux/arm64"

# Login to AWS ECR
echo "Logging in to Amazon ECR..."
if ! aws ecr get-login-password --region "${AWS_REGION}" | \
   docker login --username AWS --password-stdin "${ECR_REPOSITORY}"; then
    echo "Failed to login to ECR"
    exit 1
fi

# Build and push multi-architecture Docker image
echo "Building and pushing Docker image..."
docker buildx build \
  --platform "${PLATFORMS}" \
  -t "${ECR_REPOSITORY}/${IMAGE_NAME}:${TAG}" \
  --push \
  .
"""
Tests for the PDF Color Analysis Service.

This module contains unit tests and integration tests for the PDF color analysis service.
"""

import os
import tempfile
import pytest
from unittest.mock import patch, MagicMock
from PIL import Image
import numpy as np

from app.services.pdf_color_analysis_service import (
    is_color_pixel,
    analyze_image,
    check_pdf_for_color,
    get_color_pages,
    analyze_pdf_file
)


# Tests for is_color_pixel function
@pytest.mark.parametrize("pixel,expected,threshold", [
    # Grayscale pixels
    ((100, 100, 100), False, 0.01),  # Pure grayscale
    ((0, 0, 0), False, 0.01),        # Black
    ((255, 255, 255), False, 0.01),  # White
    ((100, 101, 100), False, 0.01),  # Almost grayscale (below threshold)

    # Color pixels
    ((255, 0, 0), True, 0.01),       # Red
    ((0, 255, 0), True, 0.01),       # Green
    ((0, 0, 255), True, 0.01),       # Blue
    ((100, 105, 100), True, 0.01),   # Subtle color (just above threshold)

    # Threshold effect
    ((100, 110, 100), True, 0.01),   # Color with low threshold
    ((100, 110, 100), False, 0.05),  # Same pixel, grayscale with high threshold

    # Dark and light pixels
    ((5, 8, 5), False, 0.01),        # Very dark pixel
    ((250, 247, 250), False, 0.01),  # Very light pixel
])
def test_is_color_pixel(pixel, expected, threshold):
    """Test is_color_pixel with various inputs using parameterization."""
    assert is_color_pixel(pixel, threshold) == expected


def test_grayscale_pixel():
    """Test that grayscale pixels are correctly identified."""
    # Test with pure grayscale pixels
    assert not is_color_pixel((100, 100, 100))
    assert not is_color_pixel((0, 0, 0))
    assert not is_color_pixel((255, 255, 255))

    # Test with almost grayscale pixels (below threshold)
    assert not is_color_pixel((100, 101, 100), threshold=0.01)


def test_color_pixel():
    """Test that color pixels are correctly identified."""
    # Test with clearly colored pixels
    assert is_color_pixel((255, 0, 0))  # Red
    assert is_color_pixel((0, 255, 0))  # Green
    assert is_color_pixel((0, 0, 255))  # Blue

    # Test with subtle color (just above threshold)
    assert is_color_pixel((100, 105, 100), threshold=0.01)


def test_threshold_effect():
    """Test that the threshold parameter works correctly."""
    # This pixel has a small color variation
    pixel = (100, 110, 100)

    # With a low threshold, it should be detected as color
    assert is_color_pixel(pixel, threshold=0.01)

    # With a high threshold, it should be detected as grayscale
    assert not is_color_pixel(pixel, threshold=0.05)


def test_dark_and_light_pixels():
    """Test that very dark or very light pixels are treated as grayscale."""
    # Very dark pixel with slight color variation
    assert not is_color_pixel((5, 8, 5))

    # Very light pixel with slight color variation
    assert not is_color_pixel((250, 247, 250))


# Fixtures for analyze_image tests
@pytest.fixture
def test_images():
    """Create test images for analyze_image tests."""
    # Create a grayscale test image
    grayscale_image = Image.new('RGB', (100, 100), color=(128, 128, 128))

    # Create a color test image
    color_image = Image.new('RGB', (100, 100), color=(255, 0, 0))

    # Create a mixed image (mostly grayscale with some color)
    mixed_image = Image.new('RGB', (100, 100), color=(128, 128, 128))
    # Add a small colored area (1% of the image)
    for x in range(10):
        for y in range(10):
            mixed_image.putpixel((x, y), (255, 0, 0))

    # Create a grayscale mode image
    grayscale_mode_image = Image.new('L', (100, 100), color=128)

    return {
        'grayscale': grayscale_image,
        'color': color_image,
        'mixed': mixed_image,
        'grayscale_mode': grayscale_mode_image
    }


# Tests for analyze_image function
@patch('numpy.random.randint')
def test_grayscale_image(mock_randint, test_images):
    """Test that a grayscale image is correctly identified."""
    # Mock random sampling to ensure we sample the entire image
    mock_randint.side_effect = lambda start, end: start

    is_color, percentage, pixels = analyze_image(test_images['grayscale'])

    assert not is_color
    assert percentage == 0.0
    assert pixels > 0


@patch('numpy.random.randint')
def test_color_image(mock_randint, test_images):
    """Test that a color image is correctly identified."""
    # Mock random sampling to ensure we sample the entire image
    mock_randint.side_effect = lambda start, end: start

    is_color, percentage, pixels = analyze_image(test_images['color'])

    assert is_color
    assert percentage > 0.5
    assert pixels > 0


def test_sample_rate(test_images):
    """Test that the sample_rate parameter works correctly."""
    # Test with different sample rates
    _, _, pixels1 = analyze_image(test_images['color'], sample_rate=0.1)
    _, _, pixels2 = analyze_image(test_images['color'], sample_rate=0.2)

    # The number of pixels analyzed should be proportional to the sample rate
    assert pytest.approx(pixels2 / pixels1, 0.1) == 2.0


def test_image_conversion(test_images):
    """Test that non-RGB images are correctly converted."""
    # Should not raise an error
    is_color, _, _ = analyze_image(test_images['grayscale_mode'])

    # A grayscale image should not be detected as color
    assert not is_color


# Tests for check_pdf_for_color function
@patch('os.path.exists')
@patch('app.services.pdf_color_analysis_service.convert_from_path')
@patch('app.services.pdf_color_analysis_service.analyze_image')
def test_pdf_with_no_color(mock_analyze_image, mock_convert_from_path, mock_exists):
    """Test a PDF with no color pages."""
    # Mock file existence check
    mock_exists.return_value = True

    # Mock the PDF to image conversion
    mock_image = MagicMock()
    mock_convert_from_path.return_value = [mock_image, mock_image]

    # Mock the image analysis to return no color
    mock_analyze_image.return_value = (False, 0.0, 1000)

    has_color, results = check_pdf_for_color('dummy.pdf')

    assert not has_color
    assert len(results) == 2
    assert not results[0]['is_color']
    assert not results[1]['is_color']


@patch('os.path.exists')
@patch('app.services.pdf_color_analysis_service.convert_from_path')
@patch('app.services.pdf_color_analysis_service.analyze_image')
def test_pdf_with_color(mock_analyze_image, mock_convert_from_path, mock_exists):
    """Test a PDF with color pages."""
    # Mock file existence check
    mock_exists.return_value = True

    # Mock the PDF to image conversion
    mock_image = MagicMock()
    mock_convert_from_path.return_value = [mock_image, mock_image, mock_image]

    # Mock the image analysis to return color for the second page
    mock_analyze_image.side_effect = [
        (False, 0.0, 1000),
        (True, 5.0, 1000),
        (False, 0.0, 1000)
    ]

    has_color, results = check_pdf_for_color('dummy.pdf')

    assert has_color
    assert len(results) == 3
    assert not results[0]['is_color']
    assert results[1]['is_color']
    assert not results[2]['is_color']


def test_nonexistent_file():
    """Test that an error is raised for a nonexistent file."""
    with pytest.raises(FileNotFoundError):
        check_pdf_for_color('nonexistent.pdf')


@patch('app.services.pdf_color_analysis_service.convert_from_path')
def test_conversion_error(mock_convert_from_path, tmp_path):
    """Test that an error is raised when PDF conversion fails."""
    # Mock the PDF to image conversion to raise an exception
    mock_convert_from_path.side_effect = Exception("Conversion error")

    # Create a temporary PDF file using pytest's tmp_path fixture
    pdf_path = tmp_path / "test.pdf"
    pdf_path.write_text("Not a real PDF file")

    with pytest.raises(Exception) as excinfo:
        check_pdf_for_color(str(pdf_path))

    assert "Error converting PDF to images" in str(excinfo.value)


# Tests for get_color_pages function
@patch('app.services.pdf_color_analysis_service.check_pdf_for_color')
def test_get_color_pages(mock_check_pdf_for_color):
    """Test getting color pages from a PDF."""
    # Mock the PDF analysis to return some color pages
    mock_check_pdf_for_color.return_value = (True, [
        {'page': 1, 'is_color': False},
        {'page': 2, 'is_color': True},
        {'page': 3, 'is_color': False},
        {'page': 4, 'is_color': True}
    ])

    color_pages = get_color_pages('dummy.pdf')

    assert color_pages == [2, 4]


# Tests for analyze_pdf_file function
@patch('app.services.pdf_color_analysis_service.check_pdf_for_color')
def test_analyze_pdf_file(mock_check_pdf_for_color):
    """Test the comprehensive PDF analysis."""
    # Mock the PDF analysis to return some color pages
    mock_check_pdf_for_color.return_value = (True, [
        {'page': 1, 'is_color': False, 'color_percentage': 0.0, 'pixels_analyzed': 1000},
        {'page': 2, 'is_color': True, 'color_percentage': 5.0, 'pixels_analyzed': 1000},
        {'page': 3, 'is_color': False, 'color_percentage': 0.0, 'pixels_analyzed': 1000},
        {'page': 4, 'is_color': True, 'color_percentage': 10.0, 'pixels_analyzed': 1000}
    ])

    result = analyze_pdf_file('dummy.pdf')

    assert result['has_color']
    assert result['total_pages'] == 4
    assert result['color_pages'] == [2, 4]
    assert result['color_page_count'] == 2
    assert len(result['detailed_results']) == 4

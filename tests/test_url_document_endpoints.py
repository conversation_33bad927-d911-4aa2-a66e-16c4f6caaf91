"""
Tests for the URL-based document endpoints.

This module contains unit tests for the URL-based document endpoints.
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTTPException
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.database import get_db
from app.routers.documents import analyze_url
from tests.utils import configure_test_client


# Override the database dependency
def override_get_db():
    """Override the database dependency for testing."""
    return MagicMock(spec=Session)


app.dependency_overrides[get_db] = override_get_db

# Configure the test client with API key authentication
client, mock_db, patchers = configure_test_client(app, override_get_db())

# Clean up patchers after tests
@pytest.fixture(scope="module", autouse=True)
def cleanup():
    """Clean up patchers after tests."""
    yield
    for patcher in patchers:
        patcher.stop()


@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document_from_url')
@patch('app.routers.documents.create_document_processing_log')
@pytest.mark.asyncio
async def test_analyze_url_success(mock_create_log, mock_process_document_from_url, mock_get_llm_config):
    """Test successful document analysis from URL."""
    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the process_document_from_url function
    mock_process_document_from_url.return_value = {
        "extracted_data": {
            "vendor_name": "Test Vendor",
            "invoice_number": "INV-123",
            "invoice_amount": "100.00"
        },
        "raw_response": {"candidates": [{"content": {"parts": [{"text": "test"}]}}]}
    }

    # Mock the create_document_processing_log function to return a log with ID
    mock_log = MagicMock()
    mock_log.id = 123
    mock_create_log.return_value = mock_log

    # Call the function
    result = await analyze_url(
        file_url="https://example.com/test.pdf",
        document_type="standard_invoice",
        db=override_get_db()
    )

    # Assert the result contains the expected fields
    assert "vendor_name" in result
    assert "invoice_number" in result
    assert "invoice_amount" in result
    assert "file_url" in result
    assert "document_processing_log_id" in result

    # Assert the values
    assert result["file_url"] == "https://example.com/test.pdf"
    assert result["document_processing_log_id"] == 123

    # Assert the mocks were called correctly
    mock_get_llm_config.assert_called_once()
    mock_process_document_from_url.assert_called_once()
    call_args = mock_process_document_from_url.call_args[1]
    assert call_args['file_url'] == "https://example.com/test.pdf"
    assert call_args['document_type'] == "standard_invoice"

    # Assert the document processing log was created
    mock_create_log.assert_called_once()
    call_args = mock_create_log.call_args[1]
    assert call_args['blob_name'] == "documents/test.pdf"
    assert call_args['llm_configuration_id'] == 1
    assert call_args['raw_llm_response'] is not None
    assert call_args['extracted_data'] is not None


@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document_from_url')
@pytest.mark.asyncio
async def test_analyze_url_error(mock_process_document_from_url, mock_get_llm_config):
    """Test error handling when processing document from URL."""
    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the process_document_from_url function to raise an exception
    mock_process_document_from_url.side_effect = Exception("Error processing document from URL")

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await analyze_url(
            file_url="https://example.com/test.pdf",
            document_type="standard_invoice",
            db=override_get_db()
        )

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Error analyzing document from URL" in str(excinfo.value.detail)

    # Assert the mocks were called correctly
    mock_get_llm_config.assert_called_once()
    mock_process_document_from_url.assert_called_once()


def test_invoice_url_endpoint():
    """Test the invoice URL endpoint."""
    # Mock the analyze_url function
    with patch('app.routers.documents.analyze_url') as mock_analyze_url:
        mock_analyze_url.return_value = {
            "vendor_name": "Test Vendor",
            "invoice_number": "INV-123",
            "invoice_amount": "100.00",
            "file_url": "https://example.com/test.pdf",
            "document_processing_log_id": "123"
        }

        # Make the request
        response = client.post(
            "/documents/invoice",
            json={
                "file_url": "https://example.com/test.pdf",
                "vendor_name": "Test Vendor",
                "invoice_amount": 100.00,
                "vat_amount": 10.00,
                "invoice_date": "2023-01-01",
                "invoice_number": "INV-123"
            }
        )

        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert data["vendor_name"] == "Test Vendor"
        assert data["invoice_number"] == "INV-123"
        assert data["invoice_amount"] == "100.00"
        assert data["file_url"] == "https://example.com/test.pdf"
        assert data["document_processing_log_id"] == "123"


def test_tax_invoice_url_endpoint():
    """Test the tax invoice URL endpoint."""
    # Mock the analyze_url function
    with patch('app.routers.documents.analyze_url') as mock_analyze_url:
        mock_analyze_url.return_value = {
            "vendor_name": "Test Vendor",
            "tax_invoice_number": "TAX-123",
            "invoice_amount": "100.00",
            "vat_amount": "10.00",
            "file_url": "https://example.com/test.pdf",
            "document_processing_log_id": "123"
        }

        # Make the request
        response = client.post(
            "/documents/tax-invoice",
            json={
                "file_url": "https://example.com/test.pdf",
                "vendor_name": "Test Vendor",
                "invoice_amount": 100.00,
                "vat_amount": 10.00,
                "tax_invoice_date": "2023-01-01",
                "tax_invoice_number": "TAX-123"
            }
        )

        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert data["vendor_name"] == "Test Vendor"
        assert data["tax_invoice_number"] == "TAX-123"
        assert data["invoice_amount"] == "100.00"
        assert data["vat_amount"] == "10.00"
        assert data["file_url"] == "https://example.com/test.pdf"
        assert data["document_processing_log_id"] == "123"

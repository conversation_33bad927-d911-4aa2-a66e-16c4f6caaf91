"""
Document processing log models for the Document Analyzer API.

This module contains Pydantic models for document processing log-related data structures.
"""

from typing import List, Dict, Optional, Any, Generic, TypeVar, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field

from app.models.validation_models import ValidationResult

# Generic type for paginated items
T = TypeVar('T')


class DocumentProcessingLogBase(BaseModel):
    """Base model for document processing log."""
    blob_name: str
    document_size: int
    llm_configuration_id: int
    raw_llm_response: Optional[Dict[str, Any]] = None
    extracted_data: Optional[Dict[str, Any]] = None
    color_analysis_results: Optional[Dict[str, Any]] = None
    processing_time: Optional[float] = None
    status: str = "success"


class DocumentProcessingLog(DocumentProcessingLogBase):
    """Model for a document processing log with database fields."""
    id: UUID
    created_at: datetime
    validation_results: Optional[List[ValidationResult]] = None

    model_config = {
        "from_attributes": True
    }


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic model for paginated responses."""
    items: List[T]
    total: int
    page: int
    page_size: int
    pages: int

    @classmethod
    def create(cls, items: List[T], total: int, page: int, page_size: int):
        """Create a paginated response."""
        pages = (total + page_size - 1) // page_size if page_size > 0 else 0
        return cls(
            items=items,
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )


class PaginatedDocumentProcessingLog(PaginatedResponse[DocumentProcessingLog]):
    """Model for paginated document processing logs."""
    pass

# Import necessary libraries
import google.generativeai as genai
# Removed: from google.generativeai.types import Part
import time
import os
import argparse

def main():
    """
    Main function to upload a PDF, interact with the generative model,
    and time the process.
    The PDF path is provided as a command-line argument.
    """
    print("Starting the PDF interaction process...")
    start_time = time.time()

    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="Upload a PDF and interact with Google Generative AI.")
    parser.add_argument("pdf_file_path", help="Full path to your PDF file.")
    args = parser.parse_args()
    pdf_file_path = args.pdf_file_path

    # --- Configuration ---
    # 1. Configure your Google API Key
    #    It's recommended to set this as an environment variable for security.
    #    Example: os.environ["GOOGLE_API_KEY"] = "YOUR_API_KEY"
    try:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            api_key = input("Please enter your Google API Key: ")
        genai.configure(api_key=api_key)
    except AttributeError as ae:
        print(f"AttributeError during API configuration: {ae}")
        print("This might indicate an issue with the SDK installation or import. Ensure 'google-generativeai' is installed correctly.")
        return
    except Exception as e:
        print(f"Error configuring API key: {e}")
        print("Please ensure you have set the GOOGLE_API_KEY environment variable or provide it when prompted.")
        return

    # 2. Validate the PDF file path (obtained from command-line argument)
    if not os.path.exists(pdf_file_path):
        print(f"Error: PDF file not found at '{pdf_file_path}'")
        return

    # 3. Define your system prompt (instructions for the model)
    system_prompt = ("""Extract the following information from the provided invoice pdf and match it with user input. Return the results in a JSON structure.

**Field Definitions for Extraction and Matching:**

1.  **`vendor_name` (Vendor Name)**
    * IMPORTANT: In most cases, the customer on the invoice will be Komatsu Indonesia, meaning it is highly unlikely that the vendor name is also Komatsu Indonesia.
    * Identify the actual vendor (seller/supplier) from the invoice.

2.  **`invoice_date` (Tanggal / Invoice Date)**
    * Extract the date from the invoice and return it in YYYY-MM-DD format.
    * If the invoice date format is different (e.g., DD/MM/YYYY, MM/DD/YYYY, DD Mon YY), convert it accordingly to YYYY-MM-DD.
    * If the user provides `invoice_date` in a different format, attempt to convert it to YYYY-MM-DD for comparison.

3.  **`invoice_number` (Nomor Invoice / Invoice Number)**
    * Extract the unique invoice identifier from the document.
    * **CRITICAL: The invoice number *must* be extracted with 100% accuracy. Every character must be verified against the image/pdf.**
    * **General Instruction:** The invoice number is typically a series of digits or alphanumeric characters that uniquely identifies the invoice. Look for a field labeled \"Invoice No.\", \"Invoice Number\", or similar.
    * **Edge Case Handling:**
        * **IF** the invoice number is found in a column labeled \"No./Date\" (or similar) **AND** the value in that column contains two pieces of information separated by a slash (\"/\"), **THEN**:
            * The invoice number is the *first* value in this column, *before* the slash.
            * The date is the *second* value in this column, *after* the slash.
            * Extract *only* the value *before* the slash as the invoice number.
            * Do *not* include the slash or the date in the extracted invoice number.
        * **ELSE** (if the above conditions are not met):
            * Extract the invoice number as it appears in the labeled field, without any special handling.

4.  **`tax_invoice_number` (Kode dan Nomor Seri Faktur Pajak / Electronic Tax Number)**
    * Extract the electronic tax invoice number (if available).
    * This is most commonly found in a Faktur Pajak document.

5.  **`total_amount` (Nominal Rupiah / Total Amount)**
    * Extract the total invoice amount in Indonesian Rupiah (IDR) from the invoice.
    * Remove any thousand separators (e.g., \"1.500.000\" → \"1500000\"). Ensure the extracted value is a string of digits, optionally with a decimal point for non-IDR currencies if applicable, but for IDR, typically whole numbers.
    * This amount should include VAT (PPN) if applicable.
    * If the user provides `total_amount` with separators, normalize it by removing them before comparison.

6.  **`total_vat_amount` (Pajak Pertambahan Nilai / Total PPN)**
    * Extract the total PPN (Value-Added Tax, VAT) amount in IDR from the invoice.
    * Remove any thousand separators. Ensure the extracted value is a string of digits.
    * This is most commonly found in a *Faktur Pajak* document.
    * If the user provides `total_vat_amount` with separators, normalize it by removing them before comparison.

**Document Type Classification:**

7.  **`document_type`**
    * Determine if the document being processed is a \"standard_invoice\" or a \"tax_invoice\" (Faktur Pajak).
    * If neither, return \"unknown\".
    * This will be reported under the `document` object in the JSON output.

**GENERAL INSTRUCTIONS FOR ALL FIELDS:**

* **Accuracy and Confidence:**
    * Pay extremely close attention to individual characters and digits.
    * For each extracted field, include a `confidence_level` (\"High\", \"Medium\", \"Low\").
    * If there is *any* ambiguity or uncertainty in reading a character or digit (e.g., a '3' looking like a '5', a '0' looking like an 'O', a '/' being unclear), lower the `confidence_level` to 'Medium' or 'Low'.
    * For `invoice_date` conversion, any uncertainty also warrants a lower `confidence_level`.
* **Handling Missing Data:**
    * If any field's value cannot be found in the invoice, its `ocr_value` in the JSON output should be the string `\"not_found\"`, and its `status` (if user input was provided for it) will be `\"not_found\"`.
    * Ensure high accuracy in extraction, even if the invoice layout varies."""
    )

    # 4. Define the user's question about the PDF
    user_question = input("What would you like to ask about the PDF? ")

    # --- Step 1: Upload the PDF file ---
    print(f"\nUploading '{pdf_file_path}' to Google AI Studio...")
    uploaded_file = None
    try:
        # The display name is optional but helpful for identification in AI Studio
        upload_start_time = time.time()
        uploaded_file = genai.upload_file(
            path=pdf_file_path,
            display_name=os.path.basename(pdf_file_path)
        )
        upload_end_time = time.time()
        print(f"File uploaded successfully! File URI: {uploaded_file.uri}")
        print(f"File ID: {uploaded_file.name}")
        print(f"Upload duration: {upload_end_time - upload_start_time:.2f} seconds")

        # Wait for the file to be processed.
        # This is important because the file needs to be in an 'ACTIVE' state
        # before it can be used by the model.
        print("Processing the uploaded file...")
        processing_start_time = time.time()
        while uploaded_file.state.name == "PROCESSING":
            time.sleep(5) # Check every 5 seconds
            # It's good practice to re-fetch the file object to get the latest state
            retrieved_file = genai.get_file(name=uploaded_file.name)
            if retrieved_file.state.name != uploaded_file.state.name:
                 print(f"File state changed: {uploaded_file.state.name} -> {retrieved_file.state.name}")
                 uploaded_file = retrieved_file # Update the local variable
            else:
                print(f"File state: {uploaded_file.state.name} (still processing...)")


        processing_end_time = time.time()

        if uploaded_file.state.name == "FAILED":
            print(f"Error: File processing failed. State: {uploaded_file.state.name}")
            # Optionally, delete the failed upload if you want to clean up
            # genai.delete_file(name=uploaded_file.name)
            # print(f"Attempted to delete failed upload: {uploaded_file.name}")
            return
        elif uploaded_file.state.name != "ACTIVE":
            print(f"Error: File is not active. Current state: {uploaded_file.state.name}")
            return

        print(f"File processed successfully and is now active.")
        print(f"File processing duration: {processing_end_time - processing_start_time:.2f} seconds")

    except AttributeError as ae:
        print(f"AttributeError during file upload/processing: {ae}")
        print("This might indicate an issue with the SDK installation or import. Ensure 'google-generativeai' is installed correctly.")
        if uploaded_file and hasattr(uploaded_file, 'state') and uploaded_file.state.name != "ACTIVE":
             print(f"The file '{uploaded_file.name}' might be in a non-queryable state: {uploaded_file.state.name}")
        return
    except Exception as e:
        print(f"An error occurred during file upload or processing: {e}")
        if uploaded_file and hasattr(uploaded_file, 'state') and uploaded_file.state.name != "ACTIVE":
             print(f"The file '{uploaded_file.name}' might be in a non-queryable state: {uploaded_file.state.name}")
        return

    # --- Step 2: Interact with the Generative Model ---
    print("\nInteracting with the generative model...")
    try:
        # Choose a model that supports system instructions and file input
        # 'gemini-1.5-flash-latest' or 'gemini-1.5-pro-latest' are good choices
        model = genai.GenerativeModel(
            model_name="gemini-2.0-flash",
            system_instruction=system_prompt
        )

        # Construct the prompt with the uploaded file and the user's question
        # Directly use the File object returned by upload_file()
        prompt_parts = [
            uploaded_file, # The File object itself
            user_question  # The user's text question
        ]

        # Generate content
        generation_start_time = time.time()
        response = model.generate_content(prompt_parts)
        generation_end_time = time.time()

        print(f"Model interaction duration: {generation_end_time - generation_start_time:.2f} seconds")

        # --- Step 3: Output the response ---
        print("\n--- Model Response ---")
        if response.parts:
            print(response.text)
        else:
            print("No content generated. Potential safety block or empty response.")
            if response.prompt_feedback:
                print(f"Prompt Feedback: {response.prompt_feedback}")


    except AttributeError as ae: # Catch AttributeError specifically for this block
        print(f"AttributeError during model interaction: {ae}")
        print("This might indicate an issue with how 'Part' is used or if the SDK version is incompatible. Ensure 'google-generativeai' is up to date.")
        return
    except Exception as e:
        print(f"An error occurred during model interaction: {e}")
    finally:
        # --- Step 4: Clean up (optional: delete the file after use) ---
        # If you want to delete the file from Google AI Studio after processing:
        if uploaded_file and hasattr(uploaded_file, 'state') and uploaded_file.state.name == "ACTIVE": # Only attempt delete if file was successfully processed
            # print(f"\nDeleting uploaded file: {uploaded_file.name}...")
            # try:
            #     genai.delete_file(name=uploaded_file.name)
            #     print("File deleted successfully.")
            # except Exception as e:
            #     print(f"Error deleting file: {e}")
            pass # Not deleting by default, user can uncomment

    # --- Step 5: Time the whole process ---
    end_time = time.time()
    total_time = end_time - start_time
    print("\n--- Process Summary ---")
    print(f"Total time taken for the entire process: {total_time:.2f} seconds")

if __name__ == "__main__":
    # Before running, ensure you have the SDK installed:
    # pip install -q google-generativeai
    #
    # To run the script:
    # python your_script_name.py /path/to/your/document.pdf
    main()

import pytest
import logging
import uuid
from unittest.mock import patch, <PERSON>Mock
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.utils.logging_config import configure_logging, api_key_id_context, APIKeyIDFilter
from app.middleware.api_key_auth import APIKeyMiddleware
from app.models.database_models import APIKey

@pytest.fixture
def mock_db():
    """Create a mock database session."""
    return MagicMock(spec=Session)

@pytest.fixture
def mock_api_key():
    """Create a mock API key."""
    api_key = MagicMock(spec=APIKey)
    api_key.id = uuid.uuid4()
    api_key.is_active = True
    return api_key

@pytest.mark.asyncio
async def test_api_key_in_logs(mock_db, mock_api_key, caplog):
    """Test that API key ID is included in log records."""
    # Configure logging with standard formatter for testing
    configure_logging(use_json_formatter=False)
    
    # Enable pytest log capture at INFO level
    caplog.set_level(logging.INFO)
    
    # Create a test logger
    logger = logging.getLogger("test_logger")
    logger.setLevel(logging.INFO)
    
    # Set a test API key ID in the context
    test_api_key_id = str(mock_api_key.id)
    token = api_key_id_context.set(test_api_key_id)
    
    try:
        # Log a test message
        test_message = "Test log message"
        logger.info(test_message)
        
        # Get all matching records
        matching_records = [
            r for r in caplog.records 
            if r.name == "test_logger" and test_message in r.message
        ]
        
        assert len(matching_records) > 0, "Test log message not found in records"
        log_record = matching_records[0]
        
        # Check that the API key ID is in the log record
        assert hasattr(log_record, "api_key_id"), "api_key_id not found in log record"
        assert log_record.api_key_id == test_api_key_id, f"Expected api_key_id {test_api_key_id}, got {log_record.api_key_id}"
    finally:
        # Reset the context and cleanup
        api_key_id_context.reset(token)
        logger.removeHandler(stream_handler)

@pytest.mark.asyncio
@patch("app.middleware.api_key_auth.get_db")
@patch("app.middleware.api_key_auth.get_api_key_by_key")
async def test_api_endpoint_with_api_key(
    mock_get_api_key_by_key, mock_get_db, mock_db, mock_api_key, caplog
):
    """Test that API requests include API key in logs"""
    # Configure logging with standard formatter for testing
    configure_logging(use_json_formatter=False)
    
    # Enable pytest log capture at INFO level
    caplog.set_level(logging.INFO)
    
    # Mock the database session
    mock_get_db.return_value.__next__.return_value = mock_db
    
    # Mock the get_api_key_by_key function to return our mock API key
    mock_get_api_key_by_key.return_value = mock_api_key
    
    # Set up FastAPI test application
    from fastapi import FastAPI, Request
    from starlette.middleware.base import BaseHTTPMiddleware
    
    app = FastAPI()
    
    # Create our test logger
    test_logger = logging.getLogger("test_api")
    test_logger.setLevel(logging.INFO)
    
    # Add a custom middleware that logs request details
    class LoggingMiddleware(BaseHTTPMiddleware):
        async def dispatch(self, request: Request, call_next):
            test_logger.info(f"Processing request to {request.url.path}")
            response = await call_next(request)
            return response
    
    # Add a mock endpoint that uses the logger
    @app.get("/health")
    async def health_check():
        test_logger.info("Health check endpoint called")
        return {"status": "ok"}
    
    # Add our middlewares in the correct order (auth should be before logging)
    app.add_middleware(
        APIKeyMiddleware,
        exclude_paths=[],  # Don't exclude /health so we can test the middleware
        exclude_methods=["OPTIONS"]
    )
    app.add_middleware(LoggingMiddleware)
    
    # Create test client
    client = TestClient(app)
    
    # Make a request with a test API key
    response = client.get(
        "/health",
        headers={"X-API-Key": "test-key"}
    )
    
    # We should get a 200 response since we mocked a valid API key
    assert response.status_code == 200, f"Expected 200, got {response.status_code}"
    
    # Get all matching records
    api_key_logs = [
        r for r in caplog.records 
        if hasattr(r, "api_key_id") and r.api_key_id == str(mock_api_key.id)
    ]
    
    # Debug output
    print("\nAll log records:")
    for record in caplog.records:
        print(f"Logger: {record.name}, Message: {record.message}, API Key ID: {getattr(record, 'api_key_id', None)}")
    
    assert len(api_key_logs) > 0, "No logs found with API key ID"

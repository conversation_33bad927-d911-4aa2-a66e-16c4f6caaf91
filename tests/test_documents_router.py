"""
Tests for the Documents Router.

This module contains unit tests for the documents router endpoints.
"""

import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock, mock_open
from fastapi import UploadFile, HTTPException
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.database import get_db
# PDF color analysis service has been removed
from app.services.google_genai_service import process_document, get_latest_llm_configuration
from app.services.google_cloud_storage_service import upload_file
from app.repositories.document_processing_log_repository import create_document_processing_log


# Override the database dependency
def override_get_db():
    """Override the database dependency for testing."""
    return MagicMock(spec=Session)


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture
def mock_pdf_file():
    """Create a mock PDF file for testing."""
    mock_file = AsyncMock(spec=UploadFile)
    mock_file.filename = "test.pdf"
    mock_file.content_type = "application/pdf"

    # Mock the read method to return some content
    mock_file.read.return_value = b"%PDF-1.5\nsome mock pdf content"

    # Mock the seek method
    mock_file.seek.return_value = None

    return mock_file


@patch('builtins.open', new_callable=mock_open, read_data=b"%PDF-1.5\nsome mock pdf content")
@patch('tempfile.NamedTemporaryFile')
@patch('app.routers.documents.create_document_processing_log')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document')
@patch('app.routers.documents.upload_file')
@pytest.mark.asyncio
async def test_analyze_invoice_success(
    mock_upload_file,
    mock_process_document,
    mock_get_llm_config,
    mock_create_log,
    mock_temp_file,
    mock_open,
    mock_pdf_file
):
    """Test successful document analysis with color analysis."""
    # Mock the temporary file
    mock_temp_file_instance = MagicMock()
    mock_temp_file_instance.name = "/tmp/test.pdf"
    mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance

    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the upload_file function
    mock_upload_file.return_value = ("gs://bucket/documents/test.pdf", "https://storage.googleapis.com/bucket/documents/test.pdf")

    # Mock the process_document function
    mock_process_document.return_value = {
        "extracted_data": {},
        "raw_response": {"candidates": [{"content": {"parts": [{"text": "test"}]}}]}
    }

    # PDF color analysis has been removed

    # Mock the create_document_processing_log function to return a log with ID
    mock_log = MagicMock()
    mock_log.id = 123
    mock_create_log.return_value = mock_log

    # Create a test client using FastAPI's TestClient
    from app.routers.documents import analyze_invoice_file

    # Call the function directly
    result = await analyze_invoice_file(
        file=mock_pdf_file,
        db=override_get_db()
    )

    # Assert the result contains the expected fields
    assert "document_processing_log_id" in result

    # Assert the values
    assert result["document_processing_log_id"] == 123

    # Assert the mocks were called correctly
    mock_pdf_file.read.assert_called_once()
    mock_pdf_file.seek.assert_called_once_with(0)
    mock_upload_file.assert_called_once()
    mock_process_document.assert_called_once()
    mock_get_llm_config.assert_called_once()

    # Assert the document processing log was created
    mock_create_log.assert_called_once()
    # Check that the log was created with the correct parameters
    call_args = mock_create_log.call_args[1]
    assert call_args['blob_name'] == "documents/test.pdf"
    assert isinstance(call_args['document_size'], int)
    assert call_args['llm_configuration_id'] == 1
    assert call_args['raw_llm_response'] is not None
    assert call_args['extracted_data'] is not None
    assert call_args['color_analysis_results'] is None  # Color analysis has been removed
    assert isinstance(call_args['processing_time'], float)





@patch('app.routers.documents.upload_file')
@pytest.mark.asyncio
async def test_analyze_invoice_invalid_file_type(mock_upload_file, mock_pdf_file):
    """Test error handling for invalid file types."""
    # Change the filename to a non-PDF file
    mock_pdf_file.filename = "test.txt"

    # Create a test client using FastAPI's TestClient
    from app.routers.documents import analyze_invoice_file

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await analyze_invoice_file(
            file=mock_pdf_file,
            db=override_get_db()
        )

    # Assert the exception details
    assert excinfo.value.status_code == 400
    assert "File must be a PDF" in str(excinfo.value.detail)

    # Assert the upload_file function was not called
    mock_upload_file.assert_not_called()


@patch('builtins.open', new_callable=mock_open, read_data=b"%PDF-1.5\nsome mock pdf content")
@patch('tempfile.NamedTemporaryFile')
@patch('app.routers.documents.create_document_processing_log')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.process_document')
@patch('app.routers.documents.upload_file')
@pytest.mark.asyncio
async def test_analyze_invoice_error_handling(
    mock_upload_file,
    mock_process_document,
    mock_get_llm_config,
    mock_create_log,
    mock_temp_file,
    mock_open,
    mock_pdf_file
):
    """Test error handling during document analysis."""
    # Mock the temporary file
    mock_temp_file_instance = MagicMock()
    mock_temp_file_instance.name = "/tmp/test.pdf"
    mock_temp_file.return_value.__enter__.return_value = mock_temp_file_instance

    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the upload_file function to raise an exception
    mock_upload_file.side_effect = Exception("Upload error")

    # Create a test client using FastAPI's TestClient
    from app.routers.documents import analyze_invoice_file

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await analyze_invoice_file(
            file=mock_pdf_file,
            db=override_get_db()
        )

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Upload error" in str(excinfo.value.detail)

    # Assert the mocks were called correctly
    mock_pdf_file.read.assert_called_once()
    mock_upload_file.assert_called_once()
    mock_process_document.assert_not_called()
    mock_create_log.assert_not_called()

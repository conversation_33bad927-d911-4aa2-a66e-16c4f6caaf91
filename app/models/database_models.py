"""
Database models for the Document Analyzer API.

This module contains SQLAlchemy models for database tables.
"""

from sqlalchemy import Column, <PERSON>, Float, Integer, JSON, ForeignKey, <PERSON><PERSON>an
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy.sql.sqltypes import TIMESTAMP
from sqlalchemy.sql.expression import text
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime

from app.database import Base


class LLMConfiguration(Base):
    """
    Model for LLM configuration settings.

    Attributes:
        model_id (str): The identifier for the LLM model
        provider (str): The provider of the LLM (e.g., OpenAI, Anthropic)
        system_instruction (str): The system instruction template to use with the LLM
        document_type (str): The type of document this configuration is for (e.g., standard_invoice, tax_invoice)
        temperature (float): The temperature setting for the LLM
        top_p (float): The top_p setting for the LLM
        top_k (int): The top_k setting for the LLM
    """
    __tablename__ = "llm_configurations"

    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(String, nullable=False)
    provider = Column(String, nullable=False)
    system_instruction = Column(String, nullable=False)
    document_type = Column(String, nullable=False, default="standard_invoice")
    temperature = Column(Float, nullable=False, default=0)
    top_p = Column(Float, nullable=False, default=0.7)
    top_k = Column(Integer, nullable=False, default=5)

    # Audit fields
    created_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text('now()'))


class DocumentProcessingLog(Base):
    """
    Model for logging document processing activities.

    Attributes:
        id (UUID): Unique identifier for the log
        blob_name (str): Path to the document within the bucket
        document_size (int): Size of the document in bytes
        llm_configuration_id (int): ID of the LLM configuration used
        raw_llm_response (JSONB): Raw response from the LLM
        extracted_data (JSONB): Structured data extracted from the document
        color_analysis_results (JSONB): Results from the PDF color analysis
        processing_time (float): Time taken to process the document in seconds
        status (String): Status of the document processing (success or error)
    """
    __tablename__ = "document_processing_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True, server_default=text('gen_random_uuid()'), default=uuid.uuid4)
    blob_name = Column(String, nullable=False)
    document_size = Column(Integer, nullable=False)
    llm_configuration_id = Column(Integer, ForeignKey("llm_configurations.id"), nullable=False)
    raw_llm_response = Column(JSONB, nullable=True)
    extracted_data = Column(JSONB, nullable=True)
    color_analysis_results = Column(JSONB, nullable=True)
    processing_time = Column(Float, nullable=True)
    status = Column(String, nullable=False, server_default=text("'success'"))

    # Relationship to LLM configuration
    llm_configuration = relationship("LLMConfiguration")

    # Relationship to validation results
    validation_results = relationship("ValidationResult", back_populates="document_processing_log")

    # Audit fields
    created_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text('now()'))


class ValidationResult(Base):
    """
    Model for storing human validation results of extracted data.

    Attributes:
        document_processing_log_id (UUID): ID of the document processing log
        field_key (str): The field that was validated (e.g., "invoice_number")
        is_valid (bool): Whether the extracted value is valid
        validated_at (datetime): When the validation was performed
    """
    __tablename__ = "validation_results"

    id = Column(Integer, primary_key=True, index=True)
    document_processing_log_id = Column(UUID(as_uuid=True), ForeignKey("document_processing_logs.id"), nullable=False)
    field_key = Column(String, nullable=False)
    is_valid = Column(Boolean, nullable=False)
    validated_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text('now()'))

    # Relationship to document processing log
    document_processing_log = relationship("DocumentProcessingLog", back_populates="validation_results")


class APIKey(Base):
    """
    Model for API keys.

    Attributes:
        id (UUID): Unique identifier for the API key
        key (str): The API key value (hashed)
        name (str): A friendly name for the API key
        description (str): A description of what the API key is used for
        is_active (bool): Whether the API key is active
        created_at (datetime): When the API key was created
        last_used_at (datetime): When the API key was last used
    """
    __tablename__ = "api_keys"

    id = Column(UUID(as_uuid=True), primary_key=True, index=True, server_default=text('gen_random_uuid()'), default=uuid.uuid4)
    key = Column(String, nullable=False, unique=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text('now()'))
    last_used_at = Column(TIMESTAMP(timezone=True), nullable=True)

    # Relationship to API key usage
    usage_records = relationship("APIKeyUsage", back_populates="api_key")


class APIKeyUsage(Base):
    """
    Model for tracking API key usage.

    Attributes:
        id (int): Unique identifier for the usage record
        api_key_id (UUID): ID of the API key
        endpoint (str): The endpoint that was accessed
        status_code (int): The HTTP status code of the response
        processing_time (float): Time taken to process the request in seconds
        timestamp (datetime): When the request was made
    """
    __tablename__ = "api_key_usage"

    id = Column(Integer, primary_key=True, index=True)
    api_key_id = Column(UUID(as_uuid=True), ForeignKey("api_keys.id"), nullable=False)
    endpoint = Column(String, nullable=False)
    status_code = Column(Integer, nullable=False)
    processing_time = Column(Float, nullable=False)
    timestamp = Column(TIMESTAMP(timezone=True), nullable=False, server_default=text('now()'))

    # Relationship to API key
    api_key = relationship("APIKey", back_populates="usage_records")

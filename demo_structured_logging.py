#!/usr/bin/env python3
"""
Demonstration script for structured JSON logging with API key context.

This script shows how the structured logging system works with different scenarios:
1. Authenticated requests (valid API key)
2. Invalid API key attempts
3. Unauthenticated requests
4. Both JSON and standard log formats
"""

import logging
import uuid
from app.utils.logging_config import (
    configure_logging,
    api_key_id_context,
    request_id_context,
    endpoint_context,
    method_context,
    user_agent_context
)

def demo_json_logging():
    """Demonstrate JSON structured logging."""
    print("=" * 60)
    print("JSON STRUCTURED LOGGING DEMO")
    print("=" * 60)
    
    # Configure JSON logging
    configure_logging(use_json_formatter=True)
    logger = logging.getLogger("demo.json")
    
    # Scenario 1: Authenticated request
    print("\n1. Authenticated Request:")
    api_key_id_context.set("550e8400-e29b-41d4-a716-************")
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/api/documents/process")
    method_context.set("POST")
    user_agent_context.set("MyApp/1.0")
    
    logger.info("Processing document upload")
    logger.warning("Document size exceeds recommended limit")
    logger.error("Failed to extract text from PDF")
    
    # Scenario 2: Invalid API key
    print("\n2. Invalid API Key:")
    api_key_id_context.set("invalid")
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/api/documents/list")
    method_context.set("GET")
    
    logger.warning("Authentication failed - invalid API key")
    
    # Scenario 3: Unauthenticated request
    print("\n3. Unauthenticated Request:")
    api_key_id_context.set(None)
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/health")
    method_context.set("GET")
    
    logger.info("Health check endpoint accessed")
    
    # Reset context
    api_key_id_context.set(None)
    request_id_context.set(None)
    endpoint_context.set(None)
    method_context.set(None)
    user_agent_context.set(None)

def demo_standard_logging():
    """Demonstrate standard structured logging."""
    print("\n" + "=" * 60)
    print("STANDARD STRUCTURED LOGGING DEMO")
    print("=" * 60)
    
    # Configure standard logging
    configure_logging(use_json_formatter=False)
    logger = logging.getLogger("demo.standard")
    
    # Scenario 1: Authenticated request
    print("\n1. Authenticated Request:")
    api_key_id_context.set("550e8400-e29b-41d4-a716-************")
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/api/llm-configurations")
    method_context.set("GET")
    user_agent_context.set("curl/7.68.0")
    
    logger.info("Fetching LLM configurations")
    logger.debug("Retrieved 5 configurations from database")
    
    # Scenario 2: Invalid API key
    print("\n2. Invalid API Key:")
    api_key_id_context.set("invalid")
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/api/documents/upload")
    method_context.set("POST")
    
    logger.error("Unauthorized access attempt")
    
    # Scenario 3: Unauthenticated request
    print("\n3. Unauthenticated Request:")
    api_key_id_context.set(None)
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/docs")
    method_context.set("GET")
    
    logger.info("API documentation accessed")
    
    # Reset context
    api_key_id_context.set(None)
    request_id_context.set(None)
    endpoint_context.set(None)
    method_context.set(None)
    user_agent_context.set(None)

def demo_log_levels():
    """Demonstrate that all log levels include context."""
    print("\n" + "=" * 60)
    print("ALL LOG LEVELS WITH CONTEXT DEMO")
    print("=" * 60)
    
    # Configure JSON logging
    configure_logging(use_json_formatter=True)
    logger = logging.getLogger("demo.levels")
    
    # Set context for an authenticated request
    api_key_id_context.set("550e8400-e29b-41d4-a716-************")
    request_id_context.set(str(uuid.uuid4()))
    endpoint_context.set("/api/test")
    method_context.set("POST")
    user_agent_context.set("TestClient/1.0")
    
    print("\nAll log levels with API key context:")
    logger.debug("Debug message with context")
    logger.info("Info message with context")
    logger.warning("Warning message with context")
    logger.error("Error message with context")
    logger.critical("Critical message with context")
    
    # Reset context
    api_key_id_context.set(None)
    request_id_context.set(None)
    endpoint_context.set(None)
    method_context.set(None)
    user_agent_context.set(None)

if __name__ == "__main__":
    print("Structured JSON Logging Demonstration")
    print("This demo shows how API key context is automatically included in all log entries.")
    
    # Run demonstrations
    demo_json_logging()
    demo_standard_logging()
    demo_log_levels()
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETE")
    print("=" * 60)
    print("\nKey Features Demonstrated:")
    print("✓ JSON and standard log formats")
    print("✓ Automatic API key ID inclusion")
    print("✓ Request correlation with request_id")
    print("✓ Endpoint and method tracking")
    print("✓ User agent logging")
    print("✓ Invalid API key handling")
    print("✓ Unauthenticated request handling")
    print("✓ All log levels supported")
    print("\nThe logging system is now ready for production use!")

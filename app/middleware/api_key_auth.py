"""
API key authentication middleware.

This module contains middleware for API key authentication.
"""

import time
import uuid
from typing import Optional, Callable
from fastapi import Request, Response, HTTPException, Depends
from fastapi.security import APIKeyHeader
from sqlalchemy.orm import Session
import logging

from app.database import get_db
from app.repositories.api_key_repository import get_api_key_by_key, create_api_key_usage
from app.utils.logging_config import api_key_id_context, request_id_context, endpoint_context, method_context, user_agent_context

# Configure logging
logger = logging.getLogger(__name__)

# Define the API key header
X_API_KEY = APIKeyHeader(name="X-API-Key", auto_error=False)


async def get_api_key_from_header(
    api_key: str = Depends(X_API_KEY),
    db: Session = Depends(get_db)
):
    """
    Get the API key from the X-API-Key header.

    Args:
        api_key (str, optional): API key from the X-API-Key header. Defaults to Depends(X_API_KEY).
        db (Session, optional): Database session. Defaults to Depends(get_db).

    Raises:
        HTTPException: If the API key is missing, invalid, or inactive

    Returns:
        APIKey: The API key
    """
    if not api_key:
        # Set context for logging
        api_key_id_context.set("invalid")
        raise HTTPException(
            status_code=401,
            detail="API key missing",
            headers={"WWW-Authenticate": "APIKey"},
        )

    db_api_key = get_api_key_by_key(db, api_key)

    if not db_api_key:
        # Set context for logging
        api_key_id_context.set("invalid")
        raise HTTPException(
            status_code=401,
            detail="Invalid API key",
            headers={"WWW-Authenticate": "APIKey"},
        )

    if not db_api_key.is_active:
        # Set context for logging
        api_key_id_context.set("invalid")
        raise HTTPException(
            status_code=401,
            detail="API key is inactive",
            headers={"WWW-Authenticate": "APIKey"},
        )

    # Set valid API key ID in context for logging
    api_key_id_context.set(str(db_api_key.id))
    return db_api_key


class APIKeyMiddleware:
    """
    Middleware for API key authentication and usage tracking.

    This middleware:
    1. Checks for an API key in the X-API-Key header
    2. Validates the API key
    3. Tracks API key usage
    """

    def __init__(
        self,
        app,
        exclude_paths: list = None,
        exclude_methods: list = None
    ):
        """
        Initialize the middleware.

        Args:
            app: The FastAPI application
            exclude_paths (list, optional): Paths to exclude from API key authentication. Defaults to None.
            exclude_methods (list, optional): HTTP methods to exclude from API key authentication. Defaults to None.
        """
        self.app = app
        self.exclude_paths = exclude_paths or ["/docs", "/redoc", "/openapi.json", "/health"]
        self.exclude_methods = exclude_methods or ["OPTIONS"]

    async def __call__(self, scope, receive, send):
        """
        Process a request.

        Args:
            scope: The ASGI scope
            receive: The ASGI receive function
            send: The ASGI send function
        """
        if scope["type"] != "http":
            # Pass through non-HTTP requests
            await self.app(scope, receive, send)
            return

        # Create a request object
        request = Request(scope=scope, receive=receive)

        # Generate a unique request ID for correlation
        request_id = str(uuid.uuid4())

        # Set request context for logging
        request_id_context.set(request_id)
        endpoint_context.set(request.url.path)
        method_context.set(request.method)
        user_agent_context.set(request.headers.get("user-agent"))

        # Skip API key check for excluded paths and methods
        path = request.url.path
        method = request.method

        if path in self.exclude_paths or method in self.exclude_methods:
            # Set api_key_id to null for unauthenticated requests
            api_key_id_context.set(None)
            await self.app(scope, receive, send)
            return

        # Start timing the request
        start_time = time.time()

        # Create a response object to capture the status code
        response = Response()
        status_code = 200  # Default status code
        api_key_id = None

        try:
            # Get the API key from the header
            api_key = request.headers.get("X-API-Key")

            if api_key:
                # Get a database session
                db = next(get_db())

                # Validate the API key
                db_api_key = get_api_key_by_key(db, api_key)

                if db_api_key and db_api_key.is_active:
                    # Store the API key ID for usage tracking and logging
                    api_key_id = db_api_key.id
                    api_key_id_context.set(str(api_key_id))
                else:
                    # Invalid or inactive API key
                    status_code = 401
                    api_key_id_context.set("invalid")
            else:
                # Missing API key
                status_code = 401
                api_key_id_context.set("invalid")

            # If the API key is invalid or missing, return a 401 response
            if status_code == 401:
                response = Response(
                    content='{"detail":"Unauthorized: Invalid or missing API key"}',
                    status_code=401,
                    media_type="application/json",
                    headers={"WWW-Authenticate": "APIKey"}
                )
                await response(scope, receive, send)

                # Track the failed request if we have an API key ID
                if api_key_id:
                    processing_time = time.time() - start_time
                    create_api_key_usage(
                        db=db,
                        api_key_id=api_key_id,
                        endpoint=path,
                        status_code=401,
                        processing_time=processing_time
                    )

                return

            # Process the request
            await self.app(scope, receive, send)

            # Track API key usage
            if api_key_id:
                processing_time = time.time() - start_time
                create_api_key_usage(
                    db=db,
                    api_key_id=api_key_id,
                    endpoint=path,
                    status_code=status_code,
                    processing_time=processing_time
                )

        except Exception as e:
            # Log the error
            logger.error(f"Error in API key middleware: {str(e)}")

            # Pass through to the application
            await self.app(scope, receive, send)

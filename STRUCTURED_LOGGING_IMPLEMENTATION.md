# Structured JSON Logging Implementation

## Overview

This document describes the implementation of structured JSON logging in the FastAPI application with automatic API key context inclusion. The system provides comprehensive request tracing and context-aware logging across all endpoints.

## Features Implemented

### ✅ Core Requirements Met

1. **Structured JSON Logging**: Replaced basic logging with JSON-formatted logs using `python-json-logger`
2. **Automatic API Key Context**: `api_key_id` is automatically included in every log entry when available
3. **Request Context Reuse**: Leverages existing API key authentication without additional database calls
4. **All Log Levels**: Context is included in DEBUG, INFO, WARNING, ERROR, and CRITICAL logs
5. **Performance Optimized**: No impact on existing performance - reuses authentication data
6. **Backward Compatible**: Maintains existing logging statements throughout codebase

### ✅ Enhanced Features Added

1. **Request Correlation**: Unique `request_id` for tracing requests across services
2. **Endpoint Tracking**: Automatic `endpoint` and `method` logging
3. **User Agent Logging**: Captures client information for debugging/analytics
4. **Format Flexibility**: Supports both JSON and standard formats for development/testing
5. **Context Management**: Proper context variable handling for concurrent requests

## Implementation Details

### Context Variables

The system uses Python's `contextvars` for thread-safe request context:

```python
api_key_id_context = ContextVar("api_key_id", default=None)
request_id_context = ContextVar("request_id", default=None)
endpoint_context = ContextVar("endpoint", default=None)
method_context = ContextVar("method", default=None)
user_agent_context = ContextVar("user_agent", default=None)
```

### API Key Context Values

- **Valid API Key**: UUID string (e.g., `"550e8400-e29b-41d4-a716-************"`)
- **Invalid API Key**: String `"invalid"`
- **Unauthenticated**: `null`

### Log Format Examples

#### JSON Format
```json
{
  "timestamp": "2025-05-28T23:19:02.781953+00:00",
  "level": "INFO",
  "logger": "app.documents",
  "message": "Processing document upload",
  "api_key_id": "550e8400-e29b-41d4-a716-************",
  "request_id": "449ba8ba-e89b-4a40-8a2b-4e4472083e9b",
  "endpoint": "/api/documents/process",
  "method": "POST",
  "user_agent": "MyApp/1.0"
}
```

#### Standard Format
```
2025-05-29 06:19:02,782 - app.documents - INFO - Processing document upload - [API Key: 550e8400-e29b-41d4-a716-************] [Request: 449ba8ba-e89b-4a40-8a2b-4e4472083e9b] [POST /api/documents/process]
```

## Files Modified

### Core Implementation
- `app/utils/logging_config.py` - Enhanced with request context support
- `app/middleware/api_key_auth.py` - Added context variable population
- `main.py` - Replaced basic logging with structured logging

### Testing
- `tests/test_structured_logging.py` - Comprehensive test suite
- `demo_structured_logging.py` - Demonstration script

## Configuration

### Environment Variables
- `LOG_LEVEL` - Controls logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

### Usage in Code
```python
from app.utils.logging_config import configure_logging

# Configure JSON logging (production)
configure_logging(use_json_formatter=True)

# Configure standard logging (development/testing)
configure_logging(use_json_formatter=False)
```

## Benefits

### For Development
- **Request Tracing**: Easy to follow requests across the application
- **Context Awareness**: Immediate visibility into which API key made requests
- **Debugging**: Rich context for troubleshooting issues

### For Production
- **Log Aggregation**: JSON format perfect for ELK stack, Splunk, etc.
- **Monitoring**: Easy to create alerts based on API key usage patterns
- **Analytics**: Track API usage by key, endpoint, and user agent
- **Security**: Monitor invalid API key attempts

### For Operations
- **Correlation**: Link related log entries across services
- **Performance**: No additional database overhead
- **Scalability**: Context variables work correctly in async/concurrent environments

## Testing

Run the test suite:
```bash
python -m pytest tests/test_structured_logging.py -v
```

Run the demonstration:
```bash
python demo_structured_logging.py
```

## Integration with External Services

### BetterStack Integration
The JSON format is ready for BetterStack's logging service. The structured logs will automatically include:
- API key identification for user tracking
- Request correlation for distributed tracing
- Rich context for debugging and monitoring

### Log Aggregation Services
Compatible with:
- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Splunk**
- **Datadog**
- **New Relic**
- **BetterStack**

## Security Considerations

- API keys are logged as UUIDs (safe for logging)
- Invalid attempts are clearly marked
- No sensitive data is logged in context
- Request correlation helps with security monitoring

## Performance Impact

- **Zero additional database calls**
- **Minimal memory overhead** (context variables)
- **No blocking operations**
- **Thread-safe** for concurrent requests

The implementation successfully meets all requirements while providing enhanced observability and maintaining optimal performance.

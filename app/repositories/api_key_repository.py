"""
Repository for API key operations.

This module contains functions for database operations related to API keys.
"""

from typing import List, Optional, Dict, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from uuid import UUID
import secrets
import string
import hashlib
from datetime import datetime

from app.models.database_models import <PERSON><PERSON>ey, APIKeyUsage


def generate_api_key() -> str:
    """
    Generate a random API key.

    Returns:
        str: A random API key
    """
    # Generate a random 32-character string
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))


def hash_api_key(key: str) -> str:
    """
    Hash an API key for storage.

    Args:
        key (str): The API key to hash

    Returns:
        str: The hashed API key
    """
    return hashlib.sha256(key.encode()).hexdigest()


def create_api_key(
    db: Session,
    name: str,
    description: Optional[str] = None,
    is_active: bool = True
) -> Tuple[APIKey, str]:
    """
    Create a new API key.

    Args:
        db (Session): Database session
        name (str): A friendly name for the API key
        description (Optional[str], optional): A description of what the API key is used for. Defaults to None.
        is_active (bool, optional): Whether the API key is active. Defaults to True.

    Returns:
        Tuple[APIKey, str]: The created API key and the raw API key value
    """
    # Generate a random API key
    key_value = generate_api_key()

    # Hash the API key for storage
    hashed_key = hash_api_key(key_value)

    # Create the API key
    db_api_key = APIKey(
        key=hashed_key,
        name=name,
        description=description,
        is_active=is_active
    )

    db.add(db_api_key)
    db.commit()
    db.refresh(db_api_key)

    return db_api_key, key_value


def get_api_key(db: Session, api_key_id: UUID) -> Optional[APIKey]:
    """
    Get an API key by ID.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID

    Returns:
        Optional[APIKey]: API key if found, None otherwise
    """
    return db.query(APIKey).filter(APIKey.id == api_key_id).first()


def get_api_key_by_key(db: Session, key: str) -> Optional[APIKey]:
    """
    Get an API key by key value.

    Args:
        db (Session): Database session
        key (str): API key value

    Returns:
        Optional[APIKey]: API key if found, None otherwise
    """
    # Hash the key for lookup
    hashed_key = hash_api_key(key)
    return db.query(APIKey).filter(APIKey.key == hashed_key).first()


def get_all_api_keys(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    include_inactive: bool = False
) -> Tuple[List[APIKey], int]:
    """
    Get all API keys.

    Args:
        db (Session): Database session
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.
        include_inactive (bool, optional): Whether to include inactive API keys. Defaults to False.

    Returns:
        Tuple[List[APIKey], int]: List of API keys and total count
    """
    query = db.query(APIKey)

    if not include_inactive:
        query = query.filter(APIKey.is_active == True)

    total = query.count()
    items = query.order_by(desc(APIKey.created_at)).offset(skip).limit(limit).all()

    return items, total


def update_api_key(
    db: Session,
    api_key_id: UUID,
    name: Optional[str] = None,
    description: Optional[str] = None,
    is_active: Optional[bool] = None
) -> Optional[APIKey]:
    """
    Update an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID
        name (Optional[str], optional): New name for the API key. Defaults to None.
        description (Optional[str], optional): New description for the API key. Defaults to None.
        is_active (Optional[bool], optional): New active status for the API key. Defaults to None.

    Returns:
        Optional[APIKey]: Updated API key if found, None otherwise
    """
    db_api_key = db.query(APIKey).filter(APIKey.id == api_key_id).first()

    if not db_api_key:
        return None

    if name is not None:
        db_api_key.name = name

    if description is not None:
        db_api_key.description = description

    if is_active is not None:
        db_api_key.is_active = is_active

    db.commit()
    db.refresh(db_api_key)

    return db_api_key


def delete_api_key(db: Session, api_key_id: UUID) -> bool:
    """
    Delete an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID

    Returns:
        bool: True if deleted, False if not found
    """
    db_api_key = db.query(APIKey).filter(APIKey.id == api_key_id).first()

    if not db_api_key:
        return False

    # Delete all usage records for this API key
    db.query(APIKeyUsage).filter(APIKeyUsage.api_key_id == api_key_id).delete()

    # Delete the API key
    db.delete(db_api_key)
    db.commit()

    return True


def update_api_key_last_used(db: Session, api_key_id: UUID) -> None:
    """
    Update the last_used_at timestamp for an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID
    """
    db.query(APIKey).filter(APIKey.id == api_key_id).update(
        {"last_used_at": datetime.now()}
    )
    db.commit()


def create_api_key_usage(
    db: Session,
    api_key_id: UUID,
    endpoint: str,
    status_code: int,
    processing_time: float
) -> APIKeyUsage:
    """
    Create a new API key usage record.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID
        endpoint (str): The endpoint that was accessed
        status_code (int): The HTTP status code of the response
        processing_time (float): Time taken to process the request in seconds

    Returns:
        APIKeyUsage: Created API key usage record
    """
    db_usage = APIKeyUsage(
        api_key_id=api_key_id,
        endpoint=endpoint,
        status_code=status_code,
        processing_time=processing_time
    )

    db.add(db_usage)
    db.commit()
    db.refresh(db_usage)

    # Update the last_used_at timestamp for the API key
    update_api_key_last_used(db, api_key_id)

    return db_usage


def get_api_key_usage(
    db: Session,
    api_key_id: UUID,
    skip: int = 0,
    limit: int = 100
) -> Tuple[List[APIKeyUsage], int]:
    """
    Get usage records for an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID
        skip (int, optional): Number of records to skip. Defaults to 0.
        limit (int, optional): Maximum number of records to return. Defaults to 100.

    Returns:
        Tuple[List[APIKeyUsage], int]: List of API key usage records and total count
    """
    query = db.query(APIKeyUsage).filter(APIKeyUsage.api_key_id == api_key_id)

    total = query.count()
    items = query.order_by(desc(APIKeyUsage.timestamp)).offset(skip).limit(limit).all()

    return items, total


def get_api_key_usage_stats(db: Session, api_key_id: UUID) -> Dict:
    """
    Get usage statistics for an API key.

    Args:
        db (Session): Database session
        api_key_id (UUID): API key ID

    Returns:
        Dict: Usage statistics
    """
    # Get total requests
    total_requests = db.query(func.count(APIKeyUsage.id)).filter(
        APIKeyUsage.api_key_id == api_key_id
    ).scalar()

    # Get successful requests (status code < 400)
    successful_requests = db.query(func.count(APIKeyUsage.id)).filter(
        and_(
            APIKeyUsage.api_key_id == api_key_id,
            APIKeyUsage.status_code < 400
        )
    ).scalar()

    # Get failed requests (status code >= 400)
    failed_requests = db.query(func.count(APIKeyUsage.id)).filter(
        and_(
            APIKeyUsage.api_key_id == api_key_id,
            APIKeyUsage.status_code >= 400
        )
    ).scalar()

    # Get average processing time
    avg_processing_time = db.query(func.avg(APIKeyUsage.processing_time)).filter(
        APIKeyUsage.api_key_id == api_key_id
    ).scalar() or 0

    # Get endpoint distribution
    endpoint_counts = db.query(
        APIKeyUsage.endpoint,
        func.count(APIKeyUsage.id)
    ).filter(
        APIKeyUsage.api_key_id == api_key_id
    ).group_by(
        APIKeyUsage.endpoint
    ).all()

    endpoints = {endpoint: count for endpoint, count in endpoint_counts}

    return {
        "total_requests": total_requests,
        "successful_requests": successful_requests,
        "failed_requests": failed_requests,
        "average_processing_time": float(avg_processing_time),
        "endpoints": endpoints
    }
